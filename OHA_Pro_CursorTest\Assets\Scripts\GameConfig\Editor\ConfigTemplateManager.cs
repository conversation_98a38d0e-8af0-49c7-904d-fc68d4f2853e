using UnityEngine;
using UnityEditor;
using System.IO;

namespace GameConfig.Editor
{
    /// <summary>
    /// 配置模板管理器 - 处理.template文件的重命名问题
    /// </summary>
    public class ConfigTemplateManager : AssetPostprocessor
    {
        /// <summary>
        /// 当资源导入完成后自动处理.template文件
        /// </summary>
        /// <param name="importedAssets"></param>
        /// <param name="deletedAssets"></param>
        /// <param name="movedAssets"></param>
        /// <param name="movedFromAssetPaths"></param>
        static void OnPostprocessAllAssets(string[] importedAssets, string[] deletedAssets, string[] movedAssets, string[] movedFromAssetPaths)
        {
            bool hasTemplateFiles = false;
            
            foreach (string assetPath in importedAssets)
            {
                if (assetPath.Contains("GameConfig/Runtime") && assetPath.EndsWith(".cs.template"))
                {
                    hasTemplateFiles = true;
                    break;
                }
            }
            
            if (hasTemplateFiles)
            {
                // 延迟处理，确保所有资源都已导入
                EditorApplication.delayCall += ProcessTemplateFiles;
            }
        }
        
        /// <summary>
        /// 处理模板文件，将.cs.template重命名为.cs
        /// </summary>
        private static void ProcessTemplateFiles()
        {
            try
            {
                string runtimePath = "Assets/Scripts/GameConfig/Runtime";
                
                if (!Directory.Exists(runtimePath))
                {
                    return;
                }
                
                string[] templateFiles = Directory.GetFiles(runtimePath, "*.cs.template");
                
                foreach (string templateFile in templateFiles)
                {
                    string targetFile = templateFile.Replace(".cs.template", ".cs");
                    
                    // 如果目标文件已存在，先删除
                    if (File.Exists(targetFile))
                    {
                        File.Delete(targetFile);
                    }
                    
                    // 重命名模板文件
                    File.Move(templateFile, targetFile);
                    
                    Debug.Log($"[ConfigTemplateManager] 已处理模板文件: {Path.GetFileName(templateFile)} -> {Path.GetFileName(targetFile)}");
                }
                
                if (templateFiles.Length > 0)
                {
                    // 刷新资源数据库
                    AssetDatabase.Refresh();
                    Debug.Log($"[ConfigTemplateManager] ✅ 已处理 {templateFiles.Length} 个模板文件");
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"[ConfigTemplateManager] 处理模板文件时发生错误: {ex.Message}");
            }
        }
        
        /// <summary>
        /// 手动处理模板文件
        /// </summary>
        [MenuItem("Tools/GameConfig/处理模板文件")]
        public static void ManualProcessTemplateFiles()
        {
            ProcessTemplateFiles();
        }
        
        /// <summary>
        /// 清理所有.template文件
        /// </summary>
        [MenuItem("Tools/GameConfig/清理模板文件")]
        public static void CleanupTemplateFiles()
        {
            try
            {
                string runtimePath = "Assets/Scripts/GameConfig/Runtime";
                
                if (!Directory.Exists(runtimePath))
                {
                    EditorUtility.DisplayDialog("提示", "Runtime目录不存在", "确定");
                    return;
                }
                
                string[] templateFiles = Directory.GetFiles(runtimePath, "*.template*", SearchOption.AllDirectories);
                
                foreach (string templateFile in templateFiles)
                {
                    File.Delete(templateFile);
                    Debug.Log($"[ConfigTemplateManager] 已删除模板文件: {templateFile}");
                }
                
                // 删除对应的.meta文件
                string[] metaFiles = Directory.GetFiles(runtimePath, "*.template*.meta", SearchOption.AllDirectories);
                foreach (string metaFile in metaFiles)
                {
                    File.Delete(metaFile);
                }
                
                AssetDatabase.Refresh();
                
                if (templateFiles.Length > 0)
                {
                    EditorUtility.DisplayDialog("完成", $"已清理 {templateFiles.Length} 个模板文件", "确定");
                    Debug.Log($"[ConfigTemplateManager] ✅ 已清理 {templateFiles.Length} 个模板文件");
                }
                else
                {
                    EditorUtility.DisplayDialog("提示", "没有找到需要清理的模板文件", "确定");
                }
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"[ConfigTemplateManager] 清理模板文件时发生错误: {ex.Message}");
                EditorUtility.DisplayDialog("错误", $"清理失败: {ex.Message}", "确定");
            }
        }
    }
} 