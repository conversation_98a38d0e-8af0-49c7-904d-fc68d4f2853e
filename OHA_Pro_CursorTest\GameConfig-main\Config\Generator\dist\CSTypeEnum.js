"use strict";var t;Object.defineProperty(exports,"__esModule",{value:!0}),exports.CSTypeEnum=void 0,function(t){t.Bool="bool",t.Int="int",t.String="string",t.Float="float",t.BoolList="IReadOnlyList<bool>",t.IntList="IReadOnlyList<int>",t.StringList="IReadOnlyList<string>",t.FloatList="IReadOnlyList<float>",t.BoolList2="IReadOnlyList<IReadOnlyList<bool>>",t.IntList2="IReadOnlyList<IReadOnlyList<int>>",t.StringList2="IReadOnlyList<IReadOnlyList<string>>",t.FloatList2="IReadOnlyList<IReadOnlyList<float>>",t.BoolList3="IReadOnlyList<IReadOnlyList<IReadOnlyList<bool>>>",t.IntList3="IReadOnlyList<IReadOnlyList<IReadOnlyList<int>>>",t.StringList3="IReadOnlyList<IReadOnlyList<IReadOnlyList<string>>>",t.FloatList3="IReadOnlyList<IReadOnlyList<IReadOnlyList<float>>>",t.BoolList4="IReadOnlyList<IReadOnlyList<IReadOnlyList<IReadOnlyList<bool>>>>",t.IntList4="IReadOnlyList<IReadOnlyList<IReadOnlyList<IReadOnlyList<int>>>>",t.StringList4="IReadOnlyList<IReadOnlyList<IReadOnlyList<IReadOnlyList<string>>>>",t.FloatList4="IReadOnlyList<IReadOnlyList<IReadOnlyList<IReadOnlyList<float>>>>",t.BoolList5="IReadOnlyList<IReadOnlyList<IReadOnlyList<IReadOnlyList<IReadOnlyList<bool>>>>>",t.IntList5="IReadOnlyList<IReadOnlyList<IReadOnlyList<IReadOnlyList<IReadOnlyList<int>>>>>",t.StringList5="IReadOnlyList<IReadOnlyList<IReadOnlyList<IReadOnlyList<IReadOnlyList<string>>>>>",t.FloatList5="IReadOnlyList<IReadOnlyList<IReadOnlyList<IReadOnlyList<IReadOnlyList<float>>>>>"}(t=exports.CSTypeEnum||(exports.CSTypeEnum={}));