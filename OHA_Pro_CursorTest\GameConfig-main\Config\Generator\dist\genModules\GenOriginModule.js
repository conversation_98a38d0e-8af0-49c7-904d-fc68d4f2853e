"use strict";var e=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(exports,"__esModule",{value:!0}),exports.GenOriginModule=void 0;const K=e(require("cli-color")),b=e(require("path")),P=e(require("node-xlsx")),L=require("../SpecialType"),E=require("../SheetType"),A=require("../utils/IOUtils"),j=require("../utils/StrUtils"),w=require("../DataModel"),F=require("../utils/CommonUtils"),$=require("../utils/LineBreak"),g=require("../TSTypeEnum");class t{constructor(){this._sheetFileExts=[".xls",".xlsx"]}static get Instance(){return null==this._instance&&(this._instance=new t),this._instance}gen(){return this._fileList=[],A.IOUtils.findFile(w.DataModel.Instance.config.excel_url,this._sheetFileExts,this._fileList),this._fileList=this._fileList.filter(e=>-1==e.indexOf("~$")),this._finalJsonDict={},this._remark={},this._hSheets=[],this._vSheets=[],this._enumSheets=[],console.log(`
================================= 开始生成源数据 =================================
`),this.collectAndPreproccessSheet()&&this.proccessEnumSheet()&&this.proccessHSheet()&&this.proccessVSheet()&&this.proccessHSheetOverride()&&this.exportData()&&this.checkAndExportExtendsTreeData()&&this.verifyData()}convertGenerateToArray(e){let t;if(e)if(isNaN(e)){var a=e.split("|");for(let e=0;e<a.length;e++){var l=+a[e];isNaN(l)||(null==t?t=[l]:t.push(l))}}else t=[+e];return t}getUniqueKey(a,l,s,e){let n="",i,r;for(let t=0;t<a.length;t++){var o=a[t];let e=l[o];null==(e=null==e?s[o]:e)&&(i=!0),null!=e&&(r=!0,n+=e,t<a.length-1&&(n+="_"))}return r&&i?{error:e+"主键数据不完整！数据："+l}:n?{result:n}:void 0}collectAndPreproccessSheet(){let r=new Map,o=new Map;if(w.DataModel.Instance.remark){let a={},l={},s=new Map;for(var e in w.DataModel.Instance.remark){var t=w.DataModel.Instance.remark[e];t.filePath&&(a[t.filePath]=e,l[e]=b.default,s.set(t.filePath,t.fileMD5))}let n=e=>{let t=[],a=e;for(;;){var l=w.DataModel.Instance.remark[a];if(null==l||!l.parent)break;a=l.parent,t.push(a)}return t},i=t=>{let a=[];for(var l in w.DataModel.Instance.remark){var e=w.DataModel.Instance.remark[l];if(null!=e&&e.filePath){let e=n(l);-1!=e.indexOf(t)&&a.push(l)}}return a};for(let t=0;t<this._fileList.length;t++){var h=this._fileList[t];let e;o.has(h)?e=o.get(h):(e=A.IOUtils.getFileMD5(h),o.set(h,e));var f=a[h];if(f)if(s.get(h)==e)r.has(h)||r.set(h,!0);else{var c=n(f),c=c.length?c[c.length-1]:f,d=i(c);for(let e=0;e<d.length;e++){var u=d[e],u=l[u];r.set(u,!1)}}else r.set(h,!1)}l=null,a=null,n=null,i=null}for(let e=0;e<this._fileList.length;e++){var a,l=this._fileList[e];o.has(l)||(a=A.IOUtils.getFileMD5(l),o.set(l,a))}for(let t=0;t<this._fileList.length;t++){let n=this._fileList[t];var s=o.get(n),p=w.DataModel.Instance.config.incrementalPublish&&r.get(n);let i;if(p)for(var m in w.DataModel.Instance.remark){var y=w.DataModel.Instance.remark[m];if(y.filePath==n)switch((i={filePath:this._fileList[t],fileMD5:o.get(n)}).sheetType=y.sheetType,i.sheetNameUppercase=m,i.isUseOldData=!0,i.sheetType){case E.SheetType.Horizontal:this._hSheets.push(i),i.parent=y.parent,i.isSingleMainKey=y.isSingleMainKey,i.mainKeySubs=y.mainKeySubs,i.mainKeyNames=y.mainKeyNames,i.mainKeyOnlyOneAndIsEnum=y.mainKeyOnlyOneAndIsEnum,i.oldData=w.DataModel.Instance.originConfig[m],i.oldRemarkData=y;break;case E.SheetType.Vertical:this._vSheets.push(i),i.oldData=w.DataModel.Instance.originConfig[m],i.oldRemarkData=y;break;case E.SheetType.Enum:this._enumSheets.push(i),i.gens=y.generate,i.oldData=w.DataModel.Instance.enum[m]}}else{console.log(`正在解析 ${n} `+K.default.green(`<${s}>`));var g=P.default.parse(n);if(null!=g&&g.length)for(let e=0;e<g.length;e++){var k,v=g[e];(i={filePath:this._fileList[t],fileMD5:o.get(n)}).sheetSourceData=v.data;let a=null==(v=i.sheetSourceData)?void 0:v.at(0);if(a){let t=0;for(let e=0;e<a.length&&null==a[e];e++)t++;a=null===a||void 0===a?void 0:a.filter(e=>e);for(let e=0;e<t;e++)a.unshift(null)}if(i.sheetName=null===a||void 0===a?void 0:a.at(0),i.sheetType=null===a||void 0===a?void 0:a.at(1),i.sheetName&&i.sheetType)switch(i.sheetType=j.StrUtils.convertToUpperCamelCase(i.sheetType),i.sheetNameUppercase=j.StrUtils.convertToUpperCamelCase(i.sheetName),i.sheetType!=E.SheetType.Horizontal&&i.sheetType!=E.SheetType.Vertical||(i.sheetNameUppercase+=w.DataModel.Instance.config.export_suffix),i.sheetType){case E.SheetType.Horizontal:{this._hSheets.push(i),i.parent=null===a||void 0===a?void 0:a.at(2),i.parent&&(i.parent=j.StrUtils.convertToUpperCamelCase(i.parent)+w.DataModel.Instance.config.export_suffix),i.keyNames=i.sheetSourceData[1],i.fixedKeyTypes={},i.formats=i.sheetSourceData[2];let l=i.sheetSourceData[3];i.gens=i.sheetSourceData[4],i.defaults=i.sheetSourceData[5],i.annotations=i.sheetSourceData[6],i.mainKeySubs=[],i.mainKeyNames=[];for(let e=i.keyNames.length-1;0<=e;e--){if(-1==(null==(k=i.keyNames.at(e))?void 0:k.indexOf("#")))return console.log(K.default.red(`${i.sheetNameUppercase}的${i.keyNames[e]}字段没有声明类型！文件路径：`+n)),!1;"#"==(null==(k=i.keyNames.at(e))?void 0:k.at(0))||1<(null==(k=null==(k=i.keyNames.at(e))?void 0:k.split("#"))?void 0:k.length)&&(i.keyNames[e]=k[0],i.fixedKeyTypes[i.keyNames[e]]=k[1])}i.sheetContent=i.sheetSourceData.slice(7,i.sheetSourceData.length),i.sheetContent=i.sheetContent.filter(e=>e.filter(e=>null!=e).length);for(let e=0;e<i.keyNames.length;e++){var D=i.keyNames[e];if(i.keyNames.indexOf(D)!=e&&0<=i.keyNames.indexOf(D))return console.log(K.default.red(i.sheetName+`检测到重复的键! 键：${D}，文件路径：`+n)),!1}for(let a=0;a<l.length;a++){let t=l[a];if(isNaN(+t))l[a]=t=null;else{if(F.CommonUtils.numIsFloat(t))return console.log(K.default.red(`${i.sheetName}主键不支持浮点数! 主键：${i.keyNames[a]}，文件路径：`+n)),!1;for(let e=0;e<l.length;e++)if(a!=e&&t==l[e])return console.log(K.default.red(`${i.sheetName}检测到重复的主键! 主键：${i.keyNames[a]}，文件路径：`+n)),!1;if(i.defaults[a])return console.log(K.default.red(`${i.sheetName}的主键设置了默认值，这是不被允许的。 主键：${i.keyNames[a]}，文件路径：`+n)),!1}}var N,_=l.filter(e=>!isNaN(e)),S=Math.min.apply(null,_),x=Math.max.apply(null,_);for(let t=S;t<=x;t++){var U=l.findIndex(e=>e==t);0<=U&&(i.mainKeySubs.push(U),i.mainKeyNames.push(i.keyNames[U]))}if(!i.mainKeySubs.length)return console.log(K.default.red(i.sheetName+"没有主键! 文件路径："+n)),!1;i.arrayColDict={},i.arrayColAll=[],i.linkDict={};for(let l=0;l<i.formats.length;l++){const C=i.formats[l];if(null!=C){var M=C.split("#");let e=M[0],a=M[1];var T=i.keyNames[l];let t=i.fixedKeyTypes[T];switch(e=e&&j.StrUtils.convertToLowerCamelCase(e)){case L.SpecialType.Array:if(!i.arrayColDict[a]){let t=[];for(let e=0;e<i.formats.length;e++){var $=i.formats[e];if($==C&&(t.push(e),-1==i.arrayColAll.indexOf(e)))for(i.arrayColAll.push(e);i.keyNames.length<e+1;)i.keyNames.push(null)}t.length&&(i.arrayColDict[a]={cols:t})}break;case L.SpecialType.Link:var I="[]"===t.substring(t.length-2,t.length),O=a.replace("[]","").trim();i.linkDict[T]={linkSheetName:O,linkSheetNameUppercase:j.StrUtils.convertToUpperCamelCase(O)+w.DataModel.Instance.config.export_suffix,isArray:I}}}}for(N in i.arrayColDict){let e=i.arrayColDict[N].cols,a=[];e.forEach(e=>{let t=i.keyNames[e];"#"==(null===t||void 0===t?void 0:t.at(0))?(e=t.split("#"),a.push(e[1])):a.push(null)}),a.length&&!a.find(e=>null==e)&&(i.fixedKeyTypes[N]=a)}break}case E.SheetType.Vertical:this._vSheets.push(i),i.fixedKeyDatas={},i.fixedKeyTypes={},i.gens={},i.annotations={},i.sheetContent=i.sheetSourceData.slice(2,i.sheetSourceData.length),i.sheetContent=i.sheetContent.filter(e=>e.filter(e=>null!=e).length),i.sheetContent.forEach(e=>{if(-1==e[0].indexOf("#"))return console.log(K.default.red(`${i.sheetNameUppercase}的${e[0]}字段没有声明类型！文件路径：`+n)),!1;var t=e[0].split("#"),a=t[0],t=1<t.length?t[1]:null,l=this.convertGenerateToArray(e[1]),s=e[2],e=e[3];i.fixedKeyDatas[a]=s,t&&(i.fixedKeyTypes[a]=t),l&&(i.gens[a]=l),e&&(i.annotations[a]=e)});break;case E.SheetType.Enum:this._enumSheets.push(i),i.gens=a[2],i.sheetContent=i.sheetSourceData.slice(2,i.sheetSourceData.length),i.sheetContent=i.sheetContent.filter(e=>e.filter(e=>null!=e).length);break;default:console.log(K.default.red(`不支持的表格配置类型！文件路径：${n}，表名：${i.sheetName}，表类型：`+i.sheetType))}}}}return!0}proccessEnumSheet(){for(let t=0;t<this._enumSheets.length;t++){let e=this._enumSheets[t];if(e.isUseOldData)e.enumData=e.oldData;else{var a=e.sheetContent;let t=[];for(let e=0;e<a.length;e++){var l=a[e];l[0]&&t.push({key:l[0],value:l[1],annotation:l[2]})}e.enumData=t}this._remark[e.sheetNameUppercase]={filePath:e.filePath,fileMD5:e.fileMD5,generate:Array.isArray(e.gens)?e.gens:this.convertGenerateToArray(e.gens),sheetType:e.sheetType}}return!0}proccessHSheet(){var n,a,i;for(let e=0;e<this._hSheets.length;e++){let o=this._hSheets[e];if(o.isUseOldData){let e={},l=o.oldData;for(var t in l.data){let a={};l.data[t].forEach((e,t)=>{a[l.fixed_keys[t]]=e}),e[t]=a}if(o.dict=e,o.optimizedDict=l,this._finalJsonDict[o.sheetNameUppercase])return i=this._remark[o.sheetNameUppercase],console.log(K.default.red(`不允许存在相同名称的配置！文件路径：${o.filePath}，表名：${o.sheetNameUppercase}，另一个文件路径：`+(null==i?void 0:i.filePath))),!1;this._finalJsonDict[o.sheetNameUppercase]=l,this._remark[o.sheetNameUppercase]=o.oldRemarkData}else{let e={},l={},s=[];for(let t=0;t<o.keyNames.length;t++){let e=o.keyNames[t];0<=o.arrayColAll.indexOf(t)&&(a=o.formats[t].split("#"),e=a[1]),e&&-1==s.indexOf(e)&&s.push(e)}for(let l=0;l<o.sheetContent.length;l++){var h=o.sheetContent[l],f=this.getUniqueKey(o.mainKeySubs,h,o.defaults,o.sheetNameUppercase),c=null==f?void 0:f.result;if(null!=f&&f.error)return console.log(K.default.red(f.error)),!1;if(c){let a={};for(let t=0;t<o.keyNames.length;t++){var d=o.keyNames[t];if(0<=o.arrayColAll.indexOf(t)){var u=o.formats[t].split("#")[1];if(!a[u]){let r=[];for(let e=l;e<o.sheetContent.length;e++){let i=o.sheetContent[e];if((null==(n=this.getUniqueKey(o.mainKeySubs,i,o.defaults,o.sheetNameUppercase))?void 0:n.result)&&e!=l)break;{let e=o.arrayColDict[u].cols,a=!e.find(e=>!o.keyNames[e]),l=1<e.length,s=l?a?{}:[]:null,n=!1;if(e.forEach(e=>{let t=i[e];null==t?t=o.defaults[e]:n=!0,null!=t&&(l?a?s[o.keyNames[e]]=t:s.push(t):r.push(t))}),!n)break;l&&(a&&Object.keys(s).length||!a&&s.length)&&r.push(s)}}a[u]=r.length?r:""}}else if(null!=d){let e=h[t];o.fixedKeyTypes[t]!=g.TSTypeEnum.String&&(e=null==e||""===e?null==o.defaults[t]?"":o.defaults[t]:e);var p=F.CommonUtils.convertStringToObj(e);p.isString&&(p.mayBeArray&&console.log(K.default.yellow(`这个字段有可能是数组，但填写不合法！文件路径：${o.filePath}，表名：${o.sheetNameUppercase}, 字段名：`+d)),p.mayBeObj&&console.log(K.default.yellow(`这个字段有可能是对象，但填写不合法！文件路径：${o.filePath}，表名：${o.sheetNameUppercase}, 字段名：`+d))),a[d]=p.obj}}e[c]=a}}for(var m in l={data:{},fixed_keys:s},e){let t=e[m],a=[];s.forEach(e=>{a.push(t[e])}),l.data[m]=a}if(o.dict=e,o.optimizedDict=l,this._finalJsonDict[o.sheetNameUppercase])return i=this._remark[o.sheetNameUppercase],console.log(K.default.red(`不允许存在相同名称的配置！文件路径：${o.filePath}，表名：${o.sheetNameUppercase}，另一个文件路径：`+i.filePath)),!1;this._finalJsonDict[o.sheetNameUppercase]=l,this._remark[o.sheetNameUppercase]||(this._remark[o.sheetNameUppercase]={});let r=this._remark[o.sheetNameUppercase];l.fixed_keys&&l.fixed_keys.forEach(t=>{var e,a="",l=o.keyNames.findIndex(e=>e==t);let s;if(o.arrayColDict[t]){let e=o.arrayColDict[t].cols;e.forEach(e=>{a?a+=", "+o.annotations[e]:a="["+o.annotations[e]}),a&&(a+="]"),s=e[0]}else s=l,a=o.annotations[l];e=this.convertGenerateToArray(o.gens[s]);let n,i=o.formats[l];l=i&&i.split("#"),l&&2==l.length&&l[0]==L.SpecialType.Enum&&(n=j.StrUtils.convertToUpperCamelCase(l[1])),l=o.linkDict[t];r.fields||(r.fields={}),r.fields[t]={type:o.fixedKeyTypes[t],generate:e,annotation:a,enum:n,link:null==l?void 0:l.linkSheetNameUppercase,linkIsArray:null==l?void 0:l.isArray}});let t;if(1==o.mainKeySubs.length){let e=o.formats[o.mainKeySubs[0]];var y=e&&e.split("#");2==(null==y?void 0:y.length)&&y[0]==L.SpecialType.Enum&&(t=j.StrUtils.convertToUpperCamelCase(y[1]))}r.filePath=o.filePath,r.fileMD5=o.fileMD5,r.sheetType=o.sheetType,r.isSingleMainKey=1==o.mainKeySubs.length,r.parent=o.parent,r.mainKeySubs=o.mainKeySubs,r.mainKeyNames=o.mainKeyNames,r.mainKeyOnlyOneAndIsEnum=1==o.mainKeyNames.length&&t}}return!0}proccessVSheet(){for(let e=0;e<this._vSheets.length;e++){let a=this._vSheets[e];if(a.isUseOldData){var l=a.oldData,s=a.oldData;if(a.dict=l,a.optimizedDict=s,this._finalJsonDict[a.sheetNameUppercase])return l=this._remark[a.sheetNameUppercase],console.log(K.default.red(`不允许存在相同名称的配置！文件路径：${a.filePath}，表名：${a.sheetNameUppercase}，另一个文件路径：`+(null==l?void 0:l.filePath))),!1;this._finalJsonDict[a.sheetNameUppercase]=s,this._remark[a.sheetNameUppercase]=a.oldRemarkData}else{let e={};var n,i;for(n in a.fixedKeyDatas){var r=a.fixedKeyDatas[n],r=0==r?0:r||"",r=F.CommonUtils.convertStringToObj(r,!0);r.isString&&(r.mayBeArray&&console.log(K.default.yellow(`这个字段有可能是数组，但填写不合法！文件路径：${a.filePath}，表名：${a.sheetNameUppercase}, 字段名：`+n)),r.mayBeObj&&console.log(K.default.yellow(`这个字段有可能是对象，但填写不合法！文件路径：${a.filePath}，表名：${a.sheetNameUppercase}, 字段名：`+n))),e[n]=r.obj}if(l=e,a.dict=e,a.optimizedDict=l,this._finalJsonDict[a.sheetNameUppercase])return s=this._remark[a.sheetNameUppercase],console.log(K.default.red(`不允许存在相同名称的配置！文件路径：${a.filePath}，表名：${a.sheetNameUppercase}，另一个文件路径：`+(null==s?void 0:s.filePath))),!1;this._finalJsonDict[a.sheetNameUppercase]=l,this._remark[a.sheetNameUppercase]||(this._remark[a.sheetNameUppercase]={});let t=this._remark[a.sheetNameUppercase];for(i in e){var o=a.gens[i],h=a.annotations[i];t.fields||(t.fields={}),t.fields[i]={type:a.fixedKeyTypes[i],generate:o,annotation:h}}t.filePath=a.filePath,t.fileMD5=a.fileMD5,t.sheetType=E.SheetType.Vertical}}return!0}proccessHSheetOverride(){if(w.DataModel.Instance.config.excel_override_enabled){let a=e=>{let t=0,a=e.parent;for(;a;){t++;var l=this._hSheets.find(e=>e.sheetNameUppercase==a),l=(a=null==l?void 0:l.parent,w.DataModel.Instance.config.excel_extend_max_layer||100);if(t>l){console.log(K.default.red(`计算配置层数超过限制${l}，大概率是表的继承关系出现了死循环，请排查！文件路径：`+e.filePath));break}}return t};this._hSheets.sort((e,t)=>{e=a(e);return a(t)-e});for(let e=0;e<this._hSheets.length;e++){const N=this._hSheets[e];var o=N.parent&&this._hSheets.find(e=>e.sheetNameUppercase==N.parent);if(o&&N!=o){let n=this._remark[N.sheetNameUppercase];var h,f=this._remark[o.sheetNameUppercase],c=N.dict;let i=N.optimizedDict,e=o.dict,r=o.optimizedDict;for(h in i.data){var d=F.CommonUtils.numIsInt(h)?+h:h,u=i.data[d];let l={},s=e[d];var p,m=!s;if(m&&(s={},o.keyNames))for(let e=0;e<o.keyNames.length;e++){var y,g=o.keyNames[e];g&&(y=0<=(null==(y=o.mainKeyNames)?void 0:y.indexOf(g))?c[g]:null==(y=null==o?void 0:o.defaults)?void 0:y.at(e),s[g]=y)}for(const _ in s)l[_]=s[_];for(let a=0;a<i.fixed_keys.length;a++){const S=i.fixed_keys[a];if(r.fixed_keys.find(e=>e==S)){let e,t;if(N.fixedKeyTypes?e=N.fixedKeyTypes[S]:null!==n&&void 0!==n&&n.fields[S]&&(p=n.fields[S],e=p.type),o.fixedKeyTypes?t=o.fixedKeyTypes[S]:null!=f&&f.fields[S]&&(p=f.fields[S],t=p.type),e&&t&&e!=t)return console.log(K.default.red(`子表 ${N.sheetNameUppercase} 的 ${S} 与 父表 ${o.sheetNameUppercase} 的 ${S} 类型不一致，请排查！子表文件路径：${N.filePath}，父表文件路径：`+o.filePath)),!1;l[S]=u[a],!w.DataModel.Instance.config.excel_override_warning_enabled||m||w.DataModel.Instance.valEquip(s[S],u[a])||console.log(K.default.yellow(`${N.sheetNameUppercase} 的 ${S}（${u[a]}）覆盖了 ${o.sheetNameUppercase} 的 ${S}（${s[S]}）`))}}e[d]=l;let t=[];for(let e=0;e<r.fixed_keys.length;e++){var k=r.fixed_keys[e];t.push(l[k])}r.data[d]=t}let t=[];for(let e=0;e<i.fixed_keys.length;e++){const x=i.fixed_keys[e];r.fixed_keys.find(e=>e==x)&&-1==t.indexOf(x)&&-1==N.mainKeyNames.indexOf(x)&&t.push(x)}for(let e=0;e<t.length;e++){var l,s=t[e];for(l in c){let e=c[l];delete e[s]}var v=i.fixed_keys.indexOf(s);if(-1!=v)for(var D in i.fixed_keys.splice(v,1),i.data){let e=i.data[D];e.splice(v,1)}null!==n&&void 0!==n&&n.fields&&delete n.fields[s]}}}}return!0}exportData(){A.IOUtils.deleteFolderFile(w.DataModel.Instance.config.origin_export_url,!1),A.IOUtils.makeDir(w.DataModel.Instance.config.origin_export_url);let t={};for(let e=0;e<this._enumSheets.length;e++){var a=this._enumSheets[e];t[a.sheetNameUppercase]=a.enumData}var e=JSON.stringify(t,null,4);A.IOUtils.writeTextFile(w.DataModel.Instance.enumURL,e,$.LineBreak.CRLF,null,"导出源配置失败！ -> {0}, {1}");for(let e=0;e<this._hSheets.length;e++){var l=this._hSheets[e],s=JSON.stringify(l.dict,null,4);A.IOUtils.writeTextFile(b.default.join(w.DataModel.Instance.config.origin_export_url,l.sheetNameUppercase+".json"),s,$.LineBreak.CRLF,null,"导出源配置失败！ -> {0}, {1}")}for(let e=0;e<this._vSheets.length;e++){var n=this._vSheets[e],i=JSON.stringify(n.dict,null,4);A.IOUtils.writeTextFile(b.default.join(w.DataModel.Instance.config.origin_export_url,n.sheetNameUppercase+".json"),i,$.LineBreak.CRLF,null,"导出源配置失败！ -> {0}, {1}")}e=JSON.stringify(this._finalJsonDict);return A.IOUtils.writeTextFile(w.DataModel.Instance.originConfigURL,e,$.LineBreak.CRLF,"导出源配置成功！","导出源配置失败！ -> {0}, {1}"),A.IOUtils.writeTextFile(w.DataModel.Instance.remarkURL,JSON.stringify(this._remark,null,4),$.LineBreak.CRLF,null,"导出Remark文件失败！ -> {0}, {1}"),!0}checkAndExportExtendsTreeData(){let n=!1,t=(w.DataModel.Instance.reset(),Object.keys(w.DataModel.Instance.originConfig));for(let e=t.length-1;0<=e;e--){var a=t[e],l=w.DataModel.Instance.remark[a];if(l){let s=[],e=a,t=l;for(;t.parent;){var i=s.indexOf(t.parent),r=w.DataModel.Instance.remark[t.parent];if(!r){n=!0,console.log(K.default.red(`父类不存在！${t.parent}，文件路径：`+t.filePath));break}if(r.sheetType!=E.SheetType.Horizontal){n=!0,console.log(K.default.red(`不允许非水平表被继承！${e}，${t.parent}，文件路径：${t.filePath}，`+r.filePath));break}if(-1!=i){n=!0,s.push(t.parent),t.parent;let a="",l="";s.forEach((e,t)=>{a+=e;e=w.DataModel.Instance.remark[e];l+=e.filePath,t!=s.length-1&&(a+="，",l+="，")}),console.log(K.default.red(`循环继承，这是不被允许的！${a}，文件路径：`+l));break}e=t.parent,t=r,s.push(e)}}}for(let e=t.length-1;0<=e;e--){var s=t[e],o=w.DataModel.Instance.originConfig[s];let l=w.DataModel.Instance.remark[s];var h=w.DataModel.Instance.getParents(s);if(h)for(let e=0;e<h.length;e++){var f,c=h[e],d=w.DataModel.Instance.remark[c],u=d.filePath;let t=!1;if(l.mainKeyNames.length==d.mainKeyNames.length){for(let e=0;e<l.mainKeyNames.length;e++)if(l.mainKeyNames[e]!=d.mainKeyNames[e]){t=!0;break}}else t=!0;t&&(n=!0,console.log(K.default.red(`${s}继承自${l.parent}，然而他们的主键并不一致，这是不被允许的！父表主键：${d.mainKeyNames}，子表主键：${l.mainKeyNames}，父表文件路径：${u}，子表文件路径：`+l.filePath)));let a=w.DataModel.Instance.originConfig[c];for(let e=0;e<o.fixed_keys.length;e++){var p=o.fixed_keys[e];-1==l.mainKeyNames.indexOf(p)&&-1!=a.fixed_keys.indexOf(p)&&(n=!0,console.log(K.default.red(`${s}继承自${l.parent}，${p}字段重复了，这是不被允许的！父表文件路径：${u}，子表文件路径：`+l.filePath)))}for(f in o.data)a.data[f]||(n=!0,console.log(K.default.red(`${s}继承自${l.parent}，${s} 中的 ${f} 在 ${l.parent} 中无法找到，这是不被允许的！父表文件路径：${u}，子表文件路径：`+l.filePath)))}}if(n)return!1;function m(t,a){var l=Object.keys(t);if(a=a||[],null!=l&&l.length)for(let e=0;e<l.length;e++)a.push(l[e]),m(t[l[e]],a);return a}let y={};t.forEach(e=>{let a=w.DataModel.Instance.getParents(e,!0);if(a){a.reverse();let t=y;a.forEach(e=>{t[e]||(t[e]={}),t=t[e]})}}),A.IOUtils.writeTextFile(w.DataModel.Instance.config.origin_extends_url,JSON.stringify(y,null,4),$.LineBreak.CRLF,null,"导出Extends文件失败！ -> {0}, {1}");var g=Object.keys(y);for(let e=0;e<g.length;e++){var k=y[g[e]],v=Object.keys(k);if(null!=v&&v.length)for(let t=0;t<v.length;t++){let a=m(k[v[t]]);a.push(v[t]);for(let e=0;e<v.length&&t!=e;e++){let t=m(k[v[e]]);t.push(v[e]);for(let e=0;e<a.length;e++){var D=a[e],N=w.DataModel.Instance.originConfig[D],_=null!=N&&N.data?Object.keys(N.data):null;for(let e=0;e<t.length;e++){var S,x,U=t[e],M=w.DataModel.Instance.originConfig[U],T=null!=M&&M.data?Object.keys(M.data):null;for(let t=0;t<_.length;t++)for(let e=0;e<T.length;e++)_[t]==T[e]&&(S=w.DataModel.Instance.remark[D],x=w.DataModel.Instance.remark[U],console.log(K.default.red(`${D}和${U}继承自同一个父表，不允许存在相同的主键数据${_[t]}。文件路径：${S.filePath}，`+x.filePath)),n=!0)}}}}}return!n}verifyData(){let l=!1,e=(w.DataModel.Instance.reset(),Object.keys(w.DataModel.Instance.originConfig));for(let i=e.length-1;0<=i;i--){let n=e[i];var s=w.DataModel.Instance.remark[n];if(s){let a=e.filter((e,t)=>{if(t!=i){var a=w.DataModel.Instance.remark[e];if(a){var l=a.fields&&Object.keys(a.fields);if(l)for(let e=l.length-1;0<=e;e--){var s=l[e],s=a.fields[s];if((null==s?void 0:s.link)==n)return!0}}}return!1});if(a.length){let t=s.filePath;s.sheetType!=E.SheetType.Horizontal||s.isSingleMainKey||(l=!0,a.forEach(e=>{e=w.DataModel.Instance.remark[e];console.log(K.default.red(`${n} 是多主键配置，不允许被链接（Link）！请检查！文件路径：${t}，`+e.filePath))}));var r=s.fields&&Object.keys(s.fields);if(r)for(let e=r.length-1;0<=e;e--){var o=r[e],o=s.fields[o],o=a.indexOf(null==o?void 0:o.link),h=a[o];-1!=o&&(l=!0,o=w.DataModel.Instance.remark[h],console.log(K.default.red(`${n} 和 ${h} 循环链接（Link）了，请检查！文件路径：${t}，`+o.filePath)))}}}}return!l&&!l}}exports.GenOriginModule=t;