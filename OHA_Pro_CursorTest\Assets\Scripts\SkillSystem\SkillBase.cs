using System;
using System.Collections;
using System.Collections.Generic;
using Unity.VisualScripting;
using UnityEngine;
using UnityEngine.Playables;
using UnityEngine.Timeline;
using static TMPro.SpriteAssetUtilities.TexturePacker_JsonArray;
using System.Linq;
using System.Text;
// 添加CrossSystemAPI相关的引用
using OHA.CrossSystemAPI;
// 添加FX相关的引用
using FX;
// 添加中介者系统相关的引用
using OHA.MediatorSystem;

// 技能动作上下文枚举
public enum SkillActionContext
{
    None,           // 无上下文
    SkillStart,     // 技能开始时的上下文
    SkillFrame,     // 技能帧执行时的上下文
    SkillHit,       // 技能命中时的上下文
    SkillStoping    // 技能停止时的上下文
}

public class SkillBase : MonoBehaviour
{
    //所有技能相关的触发器都可以放到这里面来

    public string skillID;
    public Unit selfUnit;
    public PlayerController selfController;

    // 技能类别
    public SkillCategory category;

    // 技能名称
    public string skillName;

    // 技能描述
    [TextArea(3, 5)]
    public string description;

    // 技能图标
    public Sprite icon;

    public int skillFramesDuration ; // 技能持续时间，以帧为单位
    public int postMoveDuration; // 后摇持续时间，以帧为单位

    // 技能开始时间记录
    public float skillStartTime; // 用于计算技能执行进度

    //新增：技能开始时的自定义逻辑
    public List<System.Action> skillStartActions = new List<System.Action>();

    // 特定帧要执行的动作序列
    public Dictionary<int, List<System.Action>> skillFramesAction;
    // 新增：技能命中时的自定义逻辑
    protected List<System.Action<HitEventData>> skillHitActions = new List<System.Action<HitEventData>>();

    // 新增：技能停止时的自定义逻辑
    protected List<System.Action> skillStopActions = new List<System.Action>();


    // 当前技能动作上下文
    protected SkillActionContext currentContext = SkillActionContext.None;

    private Coroutine SkillLogicCoroutine; // 用于保存协程的引用

    public GameObject[] attackTargetGameObjectList;
    private Dictionary<GameObject, int> attackTargetTriggerCounts = new Dictionary<GameObject, int>();

    public HitBox_Data[] attackHitDatas;

    protected AttackBoxManager attackBoxManager;
    protected List<GameObject> activeAttackBoxes = new List<GameObject>();

    // 标记技能是否正常完成(默认为true，只有在提前中断时才会设为false)
    public bool skillNormallyCompleted = true;

    // 中介者引用
    private EventMediator eventMediator;

    // 添加特效统计变量
    private static int totalFXRequests = 0;
    private static int successfulFXPlays = 0;
    private static int failedFXPlays = 0;
    private static Dictionary<string, int> fxErrorsByType = new Dictionary<string, int>();




    // 在类的成员变量区域添加触发器事件集合
    // 触发器注册的事件集合 - 这些不会在技能停止时被清空
    private Dictionary<string, List<System.Action>> _triggerStartActions = new Dictionary<string, List<System.Action>>();
    private Dictionary<string, List<System.Action<HitEventData>>> _triggerHitActions = new Dictionary<string, List<System.Action<HitEventData>>>();
    private Dictionary<string, List<System.Action>> _triggerStopActions = new Dictionary<string, List<System.Action>>();

    
    
    // 技能是否已停止的标志
    private bool _isSkillStopped = false;

    // 设置当前上下文
    protected void SetContext(SkillActionContext context)
    {
        currentContext = context;
        Log($"切换技能上下文: {context}", "Context");
    }

    // 获取当前上下文
    public SkillActionContext GetCurrentContext()
    {
        return currentContext;
    }

    protected virtual void Awake()
    {
        skillFramesAction = new Dictionary<int, List<System.Action>>();
        attackBoxManager = AttackBoxManager.Instance;
        PlayedFXTracker = new HashSet<string>();
        _isSkillStopped = false; // 保证每次Awake都重置
    }

    // Start is called before the first frame update
    protected virtual void Start()
    {
        attackBoxManager = AttackBoxManager.Instance;
        if (attackBoxManager == null)
        {
            Log("AttackBoxManager实例未找到！请确保场景中存在AttackBoxManager。", "Skill", GameLogManager.LogType.Error);
            return;
        }

        // 获取事件中介者并注册技能命中事件
        eventMediator = MediatorManager.Instance.GetMediator<EventMediator>();
        eventMediator.RegisterSenderEventListener<HitEventData>(EventType.UnitHit, gameObject, OnSkillHitWithSender);
    }

    void Update()
    {

    }

    virtual public void RecordeHitBoxDatas()//如果带攻击的技能，需要为每个攻击盒记录攻击数据
    {

    }

    virtual public void CastSkill()
    {
        Log($"开始施放技能: {skillID}", "Skill");

        // 记录技能开始时间
        skillStartTime = Time.time;

        // 注册技能开始时的自定义逻辑
        SkillStartCustomizedLogicRegister();

        // 注册技能帧动作的自定义逻辑
        SkillFrameCustomizedLogicRegister();

        // 注册技能命中时的自定义逻辑
        SkillHitCustomizedLogicRegister();

        // 注册技能停止时的自定义逻辑
        SkillStopingCustomizedLogicRegister();

        // 启动技能执行协程
        SkillLogicCoroutine = StartCoroutine(ExecuteSkillLogicCoroutine());
    }

    public IEnumerator ExecuteSkillLogicCoroutine()
    {
        int currentFrame = 1;
        // 生成SessionID
        string sessionID = $"skill_{skillID}_{DateTime.Now.Ticks}";

        _isSkillStopped = false;

        //对帧追赶进行优化
        float accumulatedTime = 0f;
        float frameTime = 1f/60f; // 假设目标帧率为60fps
        float lastLogTime = 0f;
        int framesSkipped = 0;

        // 确保技能开始时skillNormallyCompleted为true（显式重置）
        skillNormallyCompleted = true;

        // *******在技能开始时执行所有技能自身的开始动作**********
        Log($"执行技能自身开始动作，数量: {skillStartActions.Count}，SessionID: {sessionID}", "Skill");
        var skillStartActionsCopy = new List<System.Action>(skillStartActions); // Copy for safe iteration
        foreach (var action in skillStartActionsCopy)
        {
            action?.Invoke();
        }

        // **********执行所有注册的skill触发器开始动作**********
        ExecuteTriggerStartActions();

        // 执行技能帧动作
        while (currentFrame <= skillFramesDuration)
        {
            // 累加实际经过的时间
            float deltaTime = Time.deltaTime;
            accumulatedTime += deltaTime;

            // 计算理论上应该执行到哪一帧
            int targetFrame = Mathf.FloorToInt(accumulatedTime / frameTime) + 1;
            targetFrame = Mathf.Min(targetFrame, skillFramesDuration);

            // 检查是否有帧被跳过
            if (targetFrame > currentFrame + 1)
            {
                int skipped = targetFrame - currentFrame - 1;
                framesSkipped += skipped;
                Log($"检测到帧跳过: 从帧 {currentFrame} 跳到帧 {targetFrame}，跳过 {skipped} 帧", "FrameSkip");
            }

            // 执行所有被跳过的帧的动作
            while (currentFrame <= targetFrame)
            {
                try
                {
                    if (skillFramesAction.TryGetValue(currentFrame, out List<System.Action> actions))
                    {
                        int length = actions.Count;
                        Log($"执行第 {currentFrame} 帧动作，动作数量: {length}", "Skill");

                        // 复制动作列表，防止在执行过程中被修改
                        List<System.Action> actionsCopy = new List<System.Action>(actions);
                        foreach (System.Action action in actionsCopy)
                        {
                            try
                            {
                                // 添加执行中的错误处理
                                action?.Invoke();
                            }
                            catch (System.Exception e)
                            {
                                Log($"执行帧动作时出错: 帧 {currentFrame}, 错误: {e.Message}", "Skill", GameLogManager.LogType.Error);
                                // 继续执行下一个动作，不中断整个技能流程
                            }
                        }
                    }
                }
                catch (System.Exception e)
                {
                    Log($"处理帧动作时出错: 帧 {currentFrame}, 错误: {e.Message}", "Skill", GameLogManager.LogType.Error);
                }
                currentFrame++;
            }

            // 检查是否可以转接到其他技能
            if (CheckTransition(currentFrame))
            {
                // 如果可以转接，立即结束当前技能并退出协程
                Log($"技能转接成功，立即结束当前技能: {skillID}, SessionID: {sessionID}", "Skill");
                // 注意：不在这里调用SkillStop()，因为在CheckTransition中已经处理了
                skillNormallyCompleted = false; // 技能被转接，不是正常完成(现在非正常完成就只有转接，之后还需要处理被硬直、死亡等打断的情况，不过那种因为是通过状态机控制，因此也自然不会被处理成自然结束)
                yield break; // 直接退出协程，不执行后续代码
            }

            // 每秒记录一次帧率信息
            if (Time.time - lastLogTime > 1f)
            {
                float currentFPS = 1f / deltaTime;
                Log($"当前帧率: {currentFPS:F1} FPS, 累计跳过帧数: {framesSkipped}", "Performance");
                lastLogTime = Time.time;
            }

            // 等待下一帧
            yield return null;
        }

        // 所有帧执行完毕，表示技能正常完成
        Log($"技能所有帧数已执行完毕，总帧数: {skillFramesDuration}，SessionID: {sessionID}", "Skill");

        // 执行结束动作（不管是提前中断或者是技能正常结束都会执行）
        SkillStop();
        Log($"技能执行完成: {skillID}, 总跳过帧数: {framesSkipped}，SessionID: {sessionID}", "Skill");

        // 获取事件中介者并触发技能完成事件
        eventMediator.TriggerSkillCompleted(selfUnit.gameObject, skillID, sessionID);

        // 在SkillStop和技能完成事件触发之后，如果技能正常完成则触发SkillNormallyCompleted事件
        if (skillNormallyCompleted)
        {
            Log($"技能正常完成: {skillID}，触发SkillNormallyCompleted事件，SessionID: {sessionID}", "Skill");

            // 使用事件中介者触发技能正常完成事件
            SkillCompletionData completionData = new SkillCompletionData
            {
                SkillID = skillID,
                SessionID = sessionID
            };
            eventMediator.TriggerSenderEvent(EventType.SkillNormallyCompleted, selfUnit.gameObject, completionData);
        }
        else
        {
            Log($"技能非正常完成: {skillID}，不触发SkillNormallyCompleted事件", "Skill", GameLogManager.LogType.Warning);
        }
    }

    // **************注意这里 不管提前中断或者正常结束都会触发！ 并且没有通过事件机制进行处理**************
     virtual public void SkillStop()
    {
        if (_isSkillStopped)
        {
            Log($"SkillStop已被调用过，跳过重复执行: {skillID}", "Skill", GameLogManager.LogType.Warning);
            return;
        }
        _isSkillStopped = true;

        Log($"技能停止: {skillID}", "Skill");

        // 检查是否处于技能转接过程中
        bool isTransitioning = !string.IsNullOrEmpty(selfUnit.nextSkill) && selfUnit.nextSkill != skillID;
        if (isTransitioning)
        {
            Log($"技能正在转接到: {selfUnit.nextSkill}，进行转接处理", "Skill");
        }
        else
        {
            Log($"技能正常结束", "Skill");
        }

        // 执行所有注册的技能自身停止动作
        int stopActionsCount = skillStopActions.Count;
        Log($"执行技能自身停止动作，数量: {stopActionsCount}", "Skill");
        var skillStopActionsCopy = new List<System.Action>(skillStopActions); // Copy for safe iteration
        foreach (var action in skillStopActionsCopy)
        {
            action?.Invoke();
        }

        // **********执行所有注册的skill触发器停止动作**********
        ExecuteTriggerStopActions();

        // **********自动清理所有与该技能关联的触发器**********
        if (TriggerSystem.SkillBaseTriggerExtensions.HasTriggers(this))
        {
            Log($"自动清理技能触发器", "TriggerCleanup");
            TriggerSystem.SkillBaseTriggerExtensions.RemoveAllTriggers(this);
        }

        // 清理所有活跃的攻击盒
        ClearActiveAttackBoxes();
        selfUnit.isPostMoveEnabled = false;
        StopSkillLogicCoroutine();

        // 清理特效跟踪集合
        PlayedFXTracker.Clear();

        // 重置技能开始时间
        skillStartTime = 0f;

        // 清理技能自身的资源，但保留触发器注册的事件
        skillStartActions.Clear();
        skillFramesAction.Clear();
        skillHitActions.Clear();
        skillStopActions.Clear();

        // 注意：不清除触发器事件集合 _triggerStartActions, _triggerHitActions, _triggerStopActions

        // 确保技能施放能力被停用
        var skillCastCapability = selfUnit.GetCapability<OHA.Capabilities.SkillCastCapability>();
        if (skillCastCapability != null && skillCastCapability.IsActive)
        {
            Log($"停用技能施放能力", "Skill");
            skillCastCapability.Deactivate();
        }
    }

      public void StopSkillLogicCoroutine()
    {
        if (SkillLogicCoroutine != null)
        {
            StopCoroutine(SkillLogicCoroutine);
            SkillLogicCoroutine = null;
        }
    }

    // 新增：技能开始时的自定义逻辑注册方法
    virtual public void SkillStartCustomizedLogicRegister()
    {
        SetContext(SkillActionContext.SkillStart);
        Log($"注册技能开始逻辑: {skillID}", "Skill");

        // 基础开始动作注册
        AddSkillStartAction(() =>
        {
            selfUnit.currentSkill = skillID;
            Log("技能开始执行: " + skillID, "Battle");
        });

        // 子类重写此方法以注册自定义开始逻辑

        SetContext(SkillActionContext.None);
    }

    // 新增：技能帧动作的自定义逻辑注册方法
    virtual public void SkillFrameCustomizedLogicRegister()
    {
        SetContext(SkillActionContext.SkillFrame);
        Log($"注册技能帧动作: {skillID}", "Skill");

        // 计算后摇开始的帧数
        int postMoveStartFrame = skillFramesDuration - postMoveDuration;

        // 只有在后摇持续时间大于0且开始帧数大于0时才启用postMove
        if (postMoveDuration > 0 && postMoveStartFrame > 0)
        {
            AddSkillFramesAction(postMoveStartFrame, () =>
            {
                Log("启用后摇取消: " + skillID, "Battle");

                // 获取事件中介者并触发启用后摇事件
                eventMediator.TriggerSenderEvent(EventType.EnablePostMove, selfUnit.gameObject, true);
            });
        }
    }

    // 新增：技能命中时的自定义逻辑注册方法
    virtual public void SkillHitCustomizedLogicRegister()
    {
        SetContext(SkillActionContext.SkillHit);
        Log($"注册技能命中逻辑: {skillID}", "Skill");
    }

    // 新增：技能停止时的自定义逻辑注册方法
    virtual public void SkillStopingCustomizedLogicRegister()
    {
        SetContext(SkillActionContext.SkillStoping);
        Log($"注册技能停止逻辑: {skillID}", "Skill");
    }

    // 检查是否可以转接到其他技能
    protected virtual bool CheckTransition(int currentFrame)
    {
        // 首先检查selfUnit是否为Character类型（Character才能转接技能）
        Character character = selfUnit as Character;
        if (character == null)
        {
            // 对非Character类型单位，直接返回false，不进行转接
            return false;
        }

        // 确保Character有SkillManager
        if (character.skillManager == null)
        {
            Log("无法进行技能转接：SkillManager为空", "Skill", GameLogManager.LogType.Warning);
            return false;
        }

        // 如果没有设置技能类别，不进行转接检查
        if (category == SkillCategory.None)
        {
            Log("技能未设置类别，无法进行转接检查", "Skill", GameLogManager.LogType.Warning);
            return false;
        }

        // 如果没有有效控制器，无法获取输入
        if (selfController == null)
        {
            Log("无法进行技能转接：控制器为空", "Skill", GameLogManager.LogType.Warning);
            return false;
        }

        // 计算当前帧的百分比
        float currentPercentage = (float)currentFrame / skillFramesDuration;

        // 获取当前输入状态
        InputState_Complex currentInput = selfController.CurrentInputStateComplexBuffer;

        // 输出当前正在检测的技能转接信息
        Log($"检查技能转接 - 当前技能: {skillID}, 类别: {category}, 当前帧: {currentFrame}, 百分比: {currentPercentage:F2}, 输入状态: {currentInput}", "Skill");

        // 检查是否可以转接
        if (character.skillManager.CheckSkillTransition(category, currentPercentage, currentInput, out SkillCategory targetCategory))
        {
            // 输出目标类别信息
            Log($"找到转接目标类别: {targetCategory}", "Skill");

            // 根据targetCategory获取目标技能
            SkillBase targetSkill = character.skillManager.GetEquippedSkill(targetCategory);
            if (targetSkill != null)
            {
                // 设置下一个技能ID，而不是直接设置当前技能ID
                selfUnit.nextSkill = targetSkill.skillID;

                Log($"转接到技能: {targetSkill.skillID}, 类别: {targetCategory}", "Skill");

                // 为当前技能创建完成SessionID
                string completionSessionID = $"skill_{skillID}_completion_{DateTime.Now.Ticks}";

                // 触发当前技能完成事件，但标记为非正常完成
                eventMediator.TriggerSkillCompleted(selfUnit.gameObject, skillID, completionSessionID);
                Log($"触发当前技能完成事件 (转接): {skillID}, SessionID: {completionSessionID}", "Skill");

                // 使用能力系统来释放技能，而不是直接切换状态
                var skillCastCapability = selfUnit.GetCapability<OHA.Capabilities.SkillCastCapability>();
                if (skillCastCapability == null)
                {
                    Log("技能转接失败：单位没有SkillCastCapability", "Skill", GameLogManager.LogType.Error);
                    return false;
                }

                // 在激活新技能前，先停止当前技能  ********此处需要注意是否有多处重复调用***********
                SkillStop();

                // 查看当前激活的技能ID
                string currentActiveSkill = skillCastCapability.GetCurrentSkillID();
                Log($"当前激活的技能: {currentActiveSkill}, 将转接到: {targetSkill.skillID}", "Skill");

                // 创建技能释放参数
                var skillParams = OHA.Capabilities.SkillCastParams.CreateWithSkillID(targetSkill.skillID);

                // 转接成功后，清空输入缓存，避免同一个输入触发多个技能
                if (selfController != null)
                {
                    selfController.ClearInputStateBuffer();
                    Log("技能转接成功后清空输入缓存", "Skill");
                }

                // 尝试激活技能释放能力
                bool activationSuccess = skillCastCapability.TryActivate(skillParams);
                if (!activationSuccess)
                {
                    Log($"技能转接激活失败: {targetSkill.skillID}", "Skill", GameLogManager.LogType.Error);
                    return false;
                }

                Log($"技能转接成功: {category} -> {targetCategory}, {skillID} -> {targetSkill.skillID}", "Skill");
                return true;
            }
            else
            {
                Log($"无法获取目标技能，目标类别: {targetCategory}", "Skill", GameLogManager.LogType.Error);
            }
        }
        else
        {
            Log($"未找到可转接的技能类别，当前类别: {category}", "Skill");
        }

        return false;
    }

    // 修改这里的AddSkillStopAction方法为public
    public void AddSkillStopAction(System.Action action)
    {
        if (action != null)
        {
            skillStopActions.Add(action);
            Log($"添加技能停止动作: {action.Method.Name}", "Skill");
        }
    }

    public void Attack(Unit target, int hitCount, float interval, int createFrame, int destroyFrame, Vector3 offset, string attackBoxSizeType = "Normal")
    {
        switch(GetCurrentContext())
        {
            case SkillActionContext.SkillStart:
            case SkillActionContext.SkillFrame:
                Log($"创建攻击盒 - 目标: {(target ? target.gameObject.name : "无目标")}, 攻击次数: {hitCount}", "Attack");
                // 使用已有的攻击数据
                if (attackHitDatas != null && attackHitDatas.Length > 0)
                {
                    HitBox_Data baseHitData = attackHitDatas[0];
                    HitBox_Data hitData = new HitBox_Data(
                        SkillID: skillID,
                        TriggerInterval: interval,
                        HitCount: hitCount,
                        TargetRelationship: baseHitData.targetRelationship,
                        AttackBoxSizeType: attackBoxSizeType,
                        DamageMultiplier: baseHitData.damageMultiplier,
                        HitStunDuration: baseHitData.hitStunDuration,
                        CanBreakArmor: baseHitData.canBreakArmor,
                        KnockbackForce: baseHitData.knockbackForce,
                        MaxHitsPerTarget: baseHitData.maxHitsPerTarget,
                        PoiseDamage: baseHitData.poiseDamage
                    );

                    GameObject attackBox = null; // 声明在外部以便在不同帧动作中访问

                    // 在指定帧生成攻击盒
                    AddSkillFramesAction(createFrame, () =>
                    {
                        Vector3 attackPosition = transform.position + transform.forward * offset.z + transform.right * offset.x + transform.up * offset.y;
                        attackBox = CreateAttackBox(attackPosition, transform.rotation, hitData);
                        Log($"创建攻击盒，帧: {createFrame}", "Battle");
                    });

                    // 在指定帧销毁攻击盒
                    if (destroyFrame > createFrame)
                    {
                        AddSkillFramesAction(destroyFrame, () =>
                        {
                            if (attackBox != null)
                            {
                                Destroy(attackBox);
                                Log($"销毁攻击盒，帧: {destroyFrame}", "Battle");
                            }
                        });
                    }
                }
                else
                {
                    Log($"没有可用的攻击数据！", "Attack", GameLogManager.LogType.Error);
                }
                break;


            default:
                Log($"Attack: 未知的上下文 {GetCurrentContext()}", "Context", GameLogManager.LogType.Error);
                break;
        }
    }


    // 创建攻击盒的方法
    private GameObject CreateAttackBox(Vector3 position, Quaternion rotation, HitBox_Data hitData)
    {
        GameObject attackBox = attackBoxManager.CreateAttackBox(position, rotation, hitData, selfUnit, transform);
        activeAttackBoxes.Add(attackBox);
        return attackBox;
    }

    // 清理所有活跃的攻击盒
    private void ClearActiveAttackBoxes()
    {
        foreach (var attackBox in activeAttackBoxes)
        {
            if (attackBox != null)
            {
                Destroy(attackBox);
            }
        }
        activeAttackBoxes.Clear();
    }

// 通用特效播放方法，根据上下文自动适配
    public void PlayFX_API(string fxName, Vector3 offset = default, float duration = 0f, bool attachToTarget = true, int frame = 0, bool useLocalOffset = true)
    {
        switch(GetCurrentContext())
        {
            case SkillActionContext.SkillStart:
            case SkillActionContext.SkillFrame:
                // 在指定帧播放特效
                PlayFXAtFrame(frame > 0 ? frame : 1, fxName, offset, duration, attachToTarget, useLocalOffset);
                break;

            case SkillActionContext.SkillHit:
                // 在命中时播放特效
                PlayFXOnHit(fxName, offset, duration, attachToTarget, useLocalOffset);
                break;

            default:
                Log($"PlayFX: 未知的上下文 {GetCurrentContext()}", "Context", GameLogManager.LogType.Error);
                break;
        }
    }

    // 在指定帧播放特效，支持本地坐标系偏移
    private void PlayFXAtFrame(int frame, string fxName, Vector3 offset = default, float duration = 0f, bool attachToSelf = true, bool useLocalOffset = true)
    {
        AddSkillFramesAction(frame, () =>
        {
            if (selfUnit == null) return;

            // 增加防护措施，确保只执行一次特效生成
            string fxInstanceKey = $"{fxName}_{frame}_{Time.frameCount}";
            if (PlayedFXTracker.Contains(fxInstanceKey)) return;
            PlayedFXTracker.Add(fxInstanceKey);

            try
            {
                // 使用FXData创建特效
                FXData fxData = new FXData(
                    fxName: fxName,
                    offset: offset,
                    duration: duration,
                    attachToTarget: attachToSelf,
                    followRotation: true
                );

                // 播放特效
                PlayFXWithLocalOffset(fxData, selfUnit.transform);

                // 记录当前实际帧与目标帧的差异
                int actualFrame = Mathf.FloorToInt(Time.time * 60) % 60; // 简单估算当前帧
                Log($"播放特效: {fxName}, 目标帧: {frame}, 实际帧: {actualFrame}, 偏差: {actualFrame - frame}", "FX");
            }
            catch (System.Exception e)
            {
                Log($"播放特效失败: {fxName}, 错误: {e.Message}", "FX", GameLogManager.LogType.Error);
            }
        });
    }

    // 添加特效跟踪集合
    private HashSet<string> PlayedFXTracker = new HashSet<string>();

    // 在技能命中时播放特效，支持本地坐标系偏移
    private void PlayFXOnHit(string fxName, Vector3 offset = default, float duration = 0f, bool attachToTarget = true, bool useLocalOffset = true)
    {
        AddSkillHitAction((hitEventData) =>
        {
            if (hitEventData.target == null) return;

            // 添加防护措施，确保同一个命中事件只播放一次特效
            string hitFxKey = $"{hitEventData.GetHashCode()}_{fxName}";
            if (PlayedFXTracker.Contains(hitFxKey)) return;
            PlayedFXTracker.Add(hitFxKey);

            try
            {
                // 使用FXData创建特效
                FXData fxData = new FXData(
                    fxName: fxName,
                    offset: offset,
                    duration: duration,
                    attachToTarget: attachToTarget,
                    followRotation: true
                );

                // 播放特效
                PlayFXWithLocalOffset(fxData, hitEventData.target.transform, hitEventData.effectPosition);

                Log($"命中播放特效: {fxName}, 目标: {hitEventData.target.unitName}", "FX");
            }
            catch (System.Exception e)
            {
                Log($"命中播放特效失败: {fxName}, 错误: {e.Message}", "FX", GameLogManager.LogType.Error);
            }
        });
    }

    // 使用本地坐标系偏移播放特效
    protected void PlayFXWithLocalOffset(FXData fxData, Transform targetTransform, Vector3? overridePosition = null)
    {
        if (fxData == null || targetTransform == null)
        {
            LogFXPlayback(false, fxData?.fxName ?? "unknown", "FXData或目标Transform为空");
            return;
        }

        // 确保FXManager实例有效
        if (FXManager.Instance == null)
        {
            LogFXPlayback(false, fxData.fxName, "FXManager实例不存在");
            FindOrCreateFXManager();

            // 如果仍然找不到FXManager，提前返回
            if (FXManager.Instance == null)
            {
                return;
            }
        }

        totalFXRequests++;

        Vector3 position = overridePosition ?? targetTransform.position;
        Quaternion rotation = targetTransform.rotation;

        // 创建特效实例
        GameObject fxInstance = null;

        try
        {
            if (fxData.attachToTarget)
            {
                // 如果附加到目标，先创建特效实例，然后设置为目标的子对象，并应用本地偏移
                int retryCount = 0;
                while (fxInstance == null && retryCount < 2)
                {
                    fxInstance = FXManager.Instance.GetFXInstance(fxData.fxName);
                    retryCount++;

                    if (fxInstance == null && retryCount < 2)
                    {
                        // 记录重试信息
                        Log($"获取特效实例失败，重试 ({retryCount}/2): {fxData.fxName}", "FX", GameLogManager.LogType.Warning);
                        // 等待短暂时间后重试
                        System.Threading.Thread.Sleep(10);
                    }
                }

                if (fxInstance != null)
                {
                    // 先设置父对象
                    fxInstance.transform.SetParent(targetTransform);

                    // 然后设置本地位置和旋转
                    fxInstance.transform.localPosition = fxData.offset;
                    fxInstance.transform.localRotation = Quaternion.identity;

                    // 如果有持续时间，自动回收
                    if (fxData.duration > 0)
                    {
                        StartCoroutine(AutoRecycleFX(fxInstance, fxData.fxName, fxData.duration));
                    }
                    else
                    {
                        // 如果没有指定持续时间，尝试获取粒子系统的持续时间
                        ParticleSystem ps = fxInstance.GetComponent<ParticleSystem>();
                        if (ps != null)
                        {
                            float particleDuration = ps.main.duration + ps.main.startLifetimeMultiplier;
                            StartCoroutine(AutoRecycleFX(fxInstance, fxData.fxName, particleDuration));
                        }
                    }
                    LogFXPlayback(true, fxData.fxName);
                }
                else
                {
                    LogFXPlayback(false, fxData.fxName, "FX实例创建失败，重试后仍然失败");
                }
            }
            else
            {
                // 如果不附加到目标，使用FXManager的PlayFX方法
                Vector3 worldOffset = targetTransform.TransformDirection(fxData.offset);
                int retryCount = 0;
                while (fxInstance == null && retryCount < 2)
                {
                    fxInstance = FXManager.Instance.PlayFX(
                        fxData.fxName,
                        position,
                        rotation,
                        null, // 不设置父对象
                        fxData.duration,
                        worldOffset
                    );
                    retryCount++;

                    if (fxInstance == null && retryCount < 2)
                    {
                        // 记录重试信息
                        Log($"播放特效失败，重试 ({retryCount}/2): {fxData.fxName}", "FX", GameLogManager.LogType.Warning);
                        // 等待短暂时间后重试
                        System.Threading.Thread.Sleep(10);
                    }
                }

                // 如果需要跟随旋转但不附加到目标上
                if (fxData.followRotation && fxInstance != null)
                {
                    FXFollower follower = fxInstance.AddComponent<FXFollower>();
                    follower.Initialize(targetTransform, fxData.offset, true);
                }

                if (fxInstance != null)
                {
                    LogFXPlayback(true, fxData.fxName);
                }
                else
                {
                    LogFXPlayback(false, fxData.fxName, "FX实例创建失败，重试后仍然失败");
                }
            }
        }
        catch (System.Exception e)
        {
            LogFXPlayback(false, fxData.fxName, e.Message);
        }
    }

    // 查找或创建FXManager实例
    private void FindOrCreateFXManager()
    {
        if (FXManager.Instance != null) return;

        // 查找场景中是否有FXManager
        FXManager existingManager = FindObjectOfType<FXManager>();
        if (existingManager != null) return;

        // 如果找不到FXManager，创建一个新实例
        Log("FXManager不存在，创建新实例", "FX", GameLogManager.LogType.Warning);
        GameObject fxManagerObj = new GameObject("FXManager");
        fxManagerObj.AddComponent<FXManager>();

        // 确保新创建的FXManager不会随场景切换被销毁
        DontDestroyOnLoad(fxManagerObj);
    }

    // 自动回收特效
    private IEnumerator AutoRecycleFX(GameObject fxInstance, string fxName, float duration)
    {
        yield return new WaitForSeconds(duration);
        if (fxInstance != null)
        {
            FXManager.Instance.ReturnToPool(fxInstance, fxName);
        }
    }

    // 使用FXData在指定帧播放特效
    protected void PlayFXAtFrame(int frame, FXData fxData)
    {
        if (fxData == null)
        {
            Log("FXData为空，无法播放特效", "FX", GameLogManager.LogType.Error);
            return;
        }

        AddSkillFramesAction(frame, () =>
        {
            if (selfUnit == null) return;

            // 播放特效
            PlayFXWithLocalOffset(fxData, selfUnit.transform);

            Log($"播放特效: {fxData.fxName}, 帧: {frame}", "FX");
        });
    }

    // 使用FXData在技能命中时播放特效
    protected void PlayFXOnHit(FXData fxData)
    {
        if (fxData == null)
        {
            Log("FXData为空，无法播放特效", "FX", GameLogManager.LogType.Error);
            return;
        }

        AddSkillHitAction((hitEventData) =>
        {
            if (hitEventData.target == null) return;

            // 播放特效
            PlayFXWithLocalOffset(fxData, hitEventData.target.transform, hitEventData.effectPosition);

            Log($"命中播放特效: {fxData.fxName}, 目标: {hitEventData.target.unitName}", "FX");
        });
    }


    // 添加一个包装方法，用于在指定帧调整朝向
    public void AdjustFacing_API(int frame = 0)
    {
        switch(GetCurrentContext())
        {
            case SkillActionContext.SkillStart:
            case SkillActionContext.SkillFrame:
                // 在指定帧调整朝向
                AddSkillFramesAction(frame > 0 ? frame : 1, () => {
                    AdjustFacingToNearestTarget();
                });
                break;

            case SkillActionContext.SkillStoping:
                // 在技能停止时调整朝向
                AddSkillStopAction(() => {
                    AdjustFacingToNearestTarget();
                });
                break;

            default:
                Log($"AdjustFacing: 未知的上下文 {GetCurrentContext()}", "Context", GameLogManager.LogType.Error);
                break;
        }
    }

    // 查找最近的目标单位
    public (bool found, Unit target) FindNearestTarget_API()
    {
        if (selfUnit == null)
        {
            Log($"无法查找目标: selfUnit 为空", "Targeting", GameLogManager.LogType.Error);
            return (false, null);
        }

        // 尝试获取TargetAcquisitionCapability能力
        var targetAcquisitionCapability = selfUnit.GetCapability<OHA.Capabilities.TargetAcquisitionCapability>();

        // 如果没有目标获取能力，则安装一个
        if (targetAcquisitionCapability == null)
        {
            Log("目标获取能力不存在，添加能力组件", "Targeting");
            targetAcquisitionCapability = OHA.Capabilities.CapabilityFactory.InstallCapability<OHA.Capabilities.TargetAcquisitionCapability>(selfUnit);

            if (targetAcquisitionCapability == null)
            {
                Log("无法创建目标获取能力", "Targeting", GameLogManager.LogType.Error);
                return (false, null);
            }
        }

        // 创建能力参数
        float searchRadius = 15f; // 默认搜索半径
        OHA.Capabilities.TargetAcquisitionParams parameters = null;

        // 如果有攻击数据，则根据攻击数据设置目标关系
        if (attackHitDatas != null && attackHitDatas.Length > 0)
        {
            parameters = OHA.Capabilities.TargetAcquisitionParams.CreateWithRelationship(
                searchRadius,
                attackHitDatas[0].targetRelationship
            );

            // 设置距离和方向权重
            parameters.DistanceWeight = 0.7f;    // 优先考虑距离
            parameters.DirectionWeight = 0.3f;   // 其次考虑方向
            parameters.HealthPercentWeight = 0f; // 不考虑生命值
        }
        else
        {
            // 如果没有攻击数据，默认获取敌方目标
            parameters = OHA.Capabilities.TargetAcquisitionParams.CreateForEnemies(searchRadius);
        }

        // 执行目标获取
        var result = targetAcquisitionCapability.AcquireTargets(parameters);

        // 如果成功获取到目标
        if (result.Success)
        {
            Unit targetUnit = result.BestTarget.Target;

            // 更新当前目标
            selfUnit.SetCurrentTarget(targetUnit);

            Log($"找到最佳目标: {targetUnit.unitName}, 距离: {result.BestTarget.Distance:F2}, 方向得分: {result.BestTarget.DirectionScore:F2}", "Targeting");
            return (true, targetUnit);
        }
        else
        {
            selfUnit.ClearCurrentTarget();
            Log("未找到有效目标", "Targeting", GameLogManager.LogType.Warning);
            return (false, null);
        }
    }

    // 调整朝向最近的目标
    private void AdjustFacingToNearestTarget()
    {
        var (found, targetUnit) = FindNearestTarget_API();
        if (found && targetUnit != null)
        {
            Vector3 direction = (targetUnit.transform.position - selfUnit.transform.position).normalized;
            direction.y = 0;
            selfUnit.transform.forward = direction;
            Log($"调整朝向完成，目标: {targetUnit.gameObject.name}", "Targeting");
        }
        else
        {
            Log("未找到有效目标，无法调整朝向", "Targeting", GameLogManager.LogType.Warning);
        }
    }

    // 包装方法，用于在指定帧范围内添加位移
    public void AddMovement_API(int startFrame, float duration, string curveName, float movePrestDistance = 1f, bool isMovingAccordingToTarget = false)
    {
        switch(GetCurrentContext())
        {
            case SkillActionContext.SkillStart:
            case SkillActionContext.SkillFrame:
                // 在指定帧添加位移 - 使用新的曲线移动能力系统实现
                int frameToExecute = startFrame;
                AddSkillFramesAction(frameToExecute, () =>
                {
                    if (selfUnit == null) return;

                    // 调试日志
                    Log($"【曲线移动】在第{frameToExecute}帧开始执行移动: 曲线={curveName}, 持续={duration}秒, 速度={movePrestDistance}, 目标方向={isMovingAccordingToTarget}", "Movement");

                    // 使用曲线移动能力实现技能位移
                    var curveMoveCapability = selfUnit.GetCapability<OHA.Capabilities.CurveMoveCapability>();
                    if (curveMoveCapability == null)
                    {
                        // 如果曲线移动能力不存在，使用CapabilityFactory统一安装能力
                        Log("曲线移动能力不存在，使用CapabilityFactory添加曲线移动能力", "Movement");
                        curveMoveCapability = OHA.Capabilities.CapabilityFactory.InstallCapability<OHA.Capabilities.CurveMoveCapability>(selfUnit);

                        if (curveMoveCapability == null)
                        {
                            Log("无法创建曲线移动能力，移动失败", "Movement", GameLogManager.LogType.Error);
                            return;
                        }
                    }

                    // 使用技能控制的曲线移动
                    bool success = curveMoveCapability.AddSkillCurveMovement(
                        curveName,
                        duration,
                        movePrestDistance,
                        isMovingAccordingToTarget   // 是否根据目标方向移动
                    );

                    if (success)
                    {
                        Log($"成功添加技能曲线移动: {curveName}, 持续时间: {duration}, 速度倍率: {movePrestDistance}, 目标方向: {isMovingAccordingToTarget}", "Movement");
                    }
                    else
                    {
                        Log("添加技能曲线移动失败", "Movement", GameLogManager.LogType.Error);
                    }
                });
                break;

            default:
                Log($"当前上下文 {GetCurrentContext()} 不支持添加移动", "Movement", GameLogManager.LogType.Warning);
                break;
        }
    }


    // 用于跟踪移动状态的类
    private class MovementState
    {
        public int StartFrame;     // 开始移动的帧数
        public int EndFrame;       // 结束移动的帧数
        public float Duration;     // 移动持续时间
        public float TotalDistance; // 总位移距离
        public MovementCurveData CurveData; // 移动曲线数据
        public float AccumulatedTime; // 累积的时间
        public float LastFrameTime; // 上一帧的时间点
        public int LastExecutedFrame; // 上次执行的帧数
    }


    // 新增：技能命中事件处理方法
    protected virtual void OnSkillHit(HitEventData hitEventData)
    {
        // 检查是否是当前技能的命中事件
        if (hitEventData.hitBoxData.skillID != skillID)
        {
            return;
        }

        Log($"技能命中: {skillID}, 目标: {hitEventData.target.unitName}", "Skill");

        try
        {
            // 执行所有注册的技能自身命中逻辑
            var skillHitActionsCopy = new List<System.Action<HitEventData>>(skillHitActions); // Copy for safe iteration
            foreach (var action in skillHitActionsCopy)
            {
                action?.Invoke(hitEventData);
            }
        }
        catch (System.Exception e)
        {
            Log($"技能自身命中处理错误: {e.Message}", "Skill", GameLogManager.LogType.Error);
        }

        // **********执行所有注册的触发器命中动作***********
        ExecuteTriggerHitActions(hitEventData);
    }

    // 新增：添加技能命中动作
    public void AddSkillHitAction(System.Action<HitEventData> action)
    {
        if (action != null)
        {
            skillHitActions.Add(action);
            Log($"添加技能命中动作: {action.Method.Name}", "Skill");
    }
        }



    // 新增: 公开的添加技能开始动作方法
    public void AddSkillStartAction(System.Action action)
    {
        if (action != null)
        {
            skillStartActions.Add(action);
            Log($"添加技能开始动作: {action.Method.Name}", "Skill");
        }
    }
    
    // 新增: 移除技能开始动作
    public void RemoveSkillStartAction(System.Action action)
    {
        if (action != null && skillStartActions.Contains(action))
        {
            skillStartActions.Remove(action);
            Log($"移除技能开始动作: {action.Method.Name}", "Skill");
        }
    }
    // 新增: 移除技能命中动作
    public void RemoveSkillHitAction(System.Action<HitEventData> action)
    {
        if (action != null && skillHitActions.Contains(action))
        {
            skillHitActions.Remove(action);
            Log($"移除技能命中动作: {action.Method.Name}", "Skill");
        }
    }


    // 新增: 移除技能停止动作
    public void RemoveSkillStopAction(System.Action action)
    {
        if (action != null && skillStopActions.Contains(action))
        {
            skillStopActions.Remove(action);
            Log($"移除技能停止动作: {action.Method.Name}", "Skill");
        }
    }

    protected virtual void OnDestroy()
    {
        // 注销事件监听器
        eventMediator.UnregisterSenderEventListener<HitEventData>(EventType.UnitHit, gameObject, OnSkillHitWithSender);

        // 销毁攻击盒子
        if (activeAttackBoxes != null && activeAttackBoxes.Count > 0)
        {
            foreach (var box in activeAttackBoxes)
            {
                if (box != null && box.gameObject != null)
                {
                    Destroy(box.gameObject);
                }
            }
            activeAttackBoxes.Clear();
        }
    }

    public void AddSkillFramesAction(int frame, System.Action action)
    {
        if (frame < 1 || frame > skillFramesDuration)
        {
            Log($"无效的技能帧数: {frame}，有效范围为 1-{skillFramesDuration}", "Skill", GameLogManager.LogType.Error);
            return;
        }
        if (!skillFramesAction.ContainsKey(frame))
        {
            skillFramesAction[frame] = new List<System.Action>();
        }
        skillFramesAction[frame].Add(action);
    }

    //添加一个在特定帧范围内执行的动作
    protected void AddSkillFramesCoherentAction(int frameStart, int frameEnd, System.Action action)
    {
        for (int frame = frameStart; frame <= frameEnd; frame++)
        {
            if (frame < 1 || frame > skillFramesDuration)
            {
                Log($"无效的技能帧数: {frame}，有效范围为 1-{skillFramesDuration}", "Skill", GameLogManager.LogType.Error);
                return;
            }

            if (!skillFramesAction.ContainsKey(frame))
            {
                skillFramesAction[frame] = new List<System.Action>();
            }
            skillFramesAction[frame].Add(action);
        }
    }

    // 添加一个重载方法，支持多个攻击盒的创建
    public void Attack(Unit target, HitBox_Data[] hitDatas, int[] createFrames, int[] destroyFrames, Vector3[] offsets, string[] attackBoxSizeTypes)
    {
        if (hitDatas.Length != createFrames.Length || createFrames.Length != destroyFrames.Length ||
            destroyFrames.Length != offsets.Length || offsets.Length != attackBoxSizeTypes.Length)
        {
            Log("攻击参数数组长度必须相同", "Attack", GameLogManager.LogType.Error);
            return;
        }

        GameObject[] attackBoxes = new GameObject[hitDatas.Length];

        for (int i = 0; i < hitDatas.Length; i++)
        {
            int index = i; // 创建局部变量以在lambda表达式中使用

            // 更新Hit_Data的AttackBoxSizeType
            hitDatas[index] = new HitBox_Data(
                SkillID: hitDatas[index].skillID,
                TriggerInterval: hitDatas[index].triggerInterval,
                HitCount: hitDatas[index].hitCount,
                TargetRelationship: hitDatas[index].targetRelationship,
                AttackBoxSizeType: attackBoxSizeTypes[index]
            );

            // 在指定帧生成攻击盒
            AddSkillFramesAction(createFrames[index], () =>
            {
                Vector3 attackPosition = transform.position +
                                       transform.forward * offsets[index].z +
                                       transform.right * offsets[index].x +
                                       transform.up * offsets[index].y;

                attackBoxes[index] = CreateAttackBox(attackPosition, transform.rotation, hitDatas[index]);
                Log($"创建攻击盒 {index}，帧: {createFrames[index]}", "Battle");
            });

            // 在指定帧销毁攻击盒
            AddSkillFramesAction(destroyFrames[index], () =>
            {
                if (attackBoxes[index] != null)
                {
                    attackBoxManager.DestroyAttackBox(attackBoxes[index].name);
                    Log($"销毁攻击盒 {index}，帧: {destroyFrames[index]}", "Battle");
                }
            });
        }
    }

        // 日志相关方法
    protected void Log(string message, string tag = "Skill", GameLogManager.LogType type = GameLogManager.LogType.Info)
    {
        if (selfUnit != null)
        {
            GameLogManager.Instance.Log(selfUnit.unitID, tag, message, type);
        }
        else
        {
            // 如果 selfUnit 为空，使用技能ID作为标识
            GameLogManager.Instance.Log(skillID, tag, message, type);
        }
    }

    // 新增：获取技能当前执行的百分比
    public float GetExecutionPercentage()
    {
        if (skillStartTime <= 0 || skillFramesDuration <= 0)
        {
            return 0f;
        }

        // 计算总持续时间（秒）
        float totalDuration = skillFramesDuration / (float)FrameRateManager.targetFrameRate;

        // 计算已经过的时间
        float elapsedTime = Time.time - skillStartTime;

        // 确保百分比在0-1范围内
        return Mathf.Clamp01(elapsedTime / totalDuration);
    }

    // 添加一个新的符合 SenderEventHandler<HitEventData> 类型的方法
    protected virtual void OnSkillHitWithSender(GameObject sender, HitEventData hitEventData)
    {
        // 转发到原来的 OnSkillHit 方法
        OnSkillHit(hitEventData);
    }

    // 新增：检查单位是否匹配目标关系要求（使用HitBox_Data）
    protected static bool IsTargetFactionMatch(Unit unit, HitBox_Data hitBoxData)
    {
        if (unit == null || hitBoxData == null)
            return false;

        // 直接使用Unit的拓展方法判断关系
        // 如果是自身技能（如增益效果），任何单位都算匹配
        if (hitBoxData.targetRelationship == UnitRelationship.Player)
            return true;

        // 如果是对所有单位生效的技能，直接返回true
        if (hitBoxData.targetRelationship == UnitRelationship.All)
            return true;

        // 对于没有明确的源单位的情况，尝试使用UnitRelationship检查
        // 例如，如果targetRelationship是Enemy，则应该对敌人返回true
        if (unit.baseRelationship != UnitRelationship.None)
        {
            return (unit.baseRelationship & hitBoxData.targetRelationship) != 0;
        }

        // 默认返回false
        return false;
    }

    // 新增：检查单位是否匹配目标关系要求
    protected static bool IsTargetFactionMatch(Unit targetUnit, Unit sourceUnit, UnitRelationship targetRelationship)
    {
        if (targetUnit == null || sourceUnit == null)
            return false;

        // 使用Unit的关系判断方法
        return sourceUnit.HasRelationshipWith(targetUnit, targetRelationship);
    }

    // 添加特效播放记录方法
    private void LogFXPlayback(bool success, string fxName, string errorReason = null)
    {
        if (success)
        {
            successfulFXPlays++;
            Log($"特效播放成功: {fxName}", "FX");
        }
        else
        {
            failedFXPlays++;
            Log($"特效播放失败: {fxName}, 原因: {errorReason}", "FX", GameLogManager.LogType.Error);

            // 记录错误类型
            string errorType = errorReason?.Split(':')[0] ?? "Unknown";
            if (!fxErrorsByType.ContainsKey(errorType))
            {
                fxErrorsByType[errorType] = 0;
            }
            fxErrorsByType[errorType]++;
        }

        // 每50次请求或技能完成时打印统计信息
        if (totalFXRequests % 50 == 0 || totalFXRequests == 1)
        {
            PrintFXStatistics();
        }
    }

    // 添加统计打印方法
    private void PrintFXStatistics()
    {
        float successRate = (totalFXRequests > 0) ? (float)successfulFXPlays / totalFXRequests * 100 : 0;

        StringBuilder stats = new StringBuilder();
        stats.AppendLine($"==== 特效播放统计 ====");
        stats.AppendLine($"总请求数: {totalFXRequests}");
        stats.AppendLine($"成功数: {successfulFXPlays}");
        stats.AppendLine($"失败数: {failedFXPlays}");
        stats.AppendLine($"成功率: {successRate:F1}%");

        if (fxErrorsByType.Count > 0)
        {
            stats.AppendLine("错误类型统计:");
            foreach (var pair in fxErrorsByType.OrderByDescending(p => p.Value))
            {
                stats.AppendLine($" - {pair.Key}: {pair.Value}次");
            }
        }

        Log(stats.ToString(), "FXStats");
    }

    #region 触发器事件注册与注销方法
    // 添加统一的触发器事件注册方法
    public void RegisterTriggerAction(string triggerID, TriggerSystem.SkillTrigger.SkillTriggerType triggerType, 
        System.Action startAction = null, System.Action<HitEventData> hitAction = null, System.Action stopAction = null)
    {
        if (string.IsNullOrEmpty(triggerID)) return;
        
        // 根据触发类型注册相应的事件
        switch (triggerType)
        {
            case TriggerSystem.SkillTrigger.SkillTriggerType.SkillStarted:
                if (startAction != null)
                {
                    RegisterTriggerStartAction(triggerID, startAction);
                }
                break;
                
            case TriggerSystem.SkillTrigger.SkillTriggerType.SkillHit:
                if (hitAction != null)
                {
                    RegisterTriggerHitAction(triggerID, hitAction);
                }
                break;
                
            case TriggerSystem.SkillTrigger.SkillTriggerType.SkillEnded:
                if (stopAction != null)
                {
                    RegisterTriggerStopAction(triggerID, stopAction);
                }
                break;
        }
        
        Log($"注册统一触发器事件: TriggerID={triggerID}, 类型={triggerType}", "Trigger");
    }

    // 为触发器注册开始事件
    public void RegisterTriggerStartAction(string triggerID, System.Action action)
    {
        RegisterTriggerGenericAction<System.Action>(
            triggerID, 
            action, 
            _triggerStartActions,
            "开始"
        );
    }

    // 为触发器注册命中事件
    public void RegisterTriggerHitAction(string triggerID, System.Action<HitEventData> action)
    {
        RegisterTriggerGenericAction<System.Action<HitEventData>>(
            triggerID, 
            action, 
            _triggerHitActions,
            "命中"
        );
    }

    // 为触发器注册停止事件
    public void RegisterTriggerStopAction(string triggerID, System.Action action)
    {
        RegisterTriggerGenericAction<System.Action>(
            triggerID, 
            action, 
            _triggerStopActions,
            "停止"
        );
    }
    
    // 通用触发器事件注册方法
    private void RegisterTriggerGenericAction<T>(
        string triggerID, 
        T action, 
        Dictionary<string, List<T>> actionsDict,
        string actionTypeName)
    {
        if (action == null || string.IsNullOrEmpty(triggerID)) return;
        
        if (!actionsDict.TryGetValue(triggerID, out var actions))
        {
            actions = new List<T>();
            actionsDict[triggerID] = actions;
        }
        
        if (!actions.Contains(action)) // 避免重复注册相同的triggerID和action
        {
            actions.Add(action);
        }
        
        Log($"注册触发器{actionTypeName}事件: TriggerID={triggerID}", "Trigger");
    }

    // 注销触发器的所有事件
    public void UnregisterTriggerActions(string triggerID)
    {
        if (string.IsNullOrEmpty(triggerID)) return;
        
        // 从技能的开始动作中移除触发器动作
        if (_triggerStartActions.ContainsKey(triggerID))
        {
            _triggerStartActions.Remove(triggerID);
        }
        
        // 从技能的命中动作中移除触发器动作
        if (_triggerHitActions.ContainsKey(triggerID))
        {
            _triggerHitActions.Remove(triggerID);
        }
        
        // 从技能的停止动作中移除触发器动作
        if (_triggerStopActions.ContainsKey(triggerID))
        {
            _triggerStopActions.Remove(triggerID);
        }
        
        Log($"注销触发器所有事件: TriggerID={triggerID}", "Trigger");
    }

    // 添加用于清理所有触发器事件的方法，在技能被完全移除时调用
    public void ClearAllTriggerActions()
    {
        // 清理所有触发器开始事件
        _triggerStartActions.Clear();
        _triggerHitActions.Clear();
        _triggerStopActions.Clear();
        
        Log("清理所有触发器事件", "Trigger");
    }

    // 新增：私有方法执行触发器回调
    private void ExecuteTriggerStartActions()
    {
        if (_triggerStartActions.Count > 0)
        {
            Log($"执行触发器注册的开始动作，触发器数量: {_triggerStartActions.Count}", "Skill");
            foreach (var actionsList in _triggerStartActions.Values.ToList()) // ToList 安全迭代
            {
                foreach (var action in actionsList.ToList()) // ToList 安全迭代
                {
                    try
                    {
                        action?.Invoke();
                    }
                    catch (System.Exception e)
                    {
                        Log($"执行触发器开始动作时出错: {e.Message}", "Skill", GameLogManager.LogType.Error);
                    }
                }
            }
        }
    }

    private void ExecuteTriggerHitActions(HitEventData hitEventData)
    {
        if (_triggerHitActions.Count > 0)
        {
            Log($"执行触发器注册的命中动作，触发器数量: {_triggerHitActions.Count}", "Skill");
            foreach (var actionsList in _triggerHitActions.Values.ToList()) // ToList 安全迭代
            {
                foreach (var action in actionsList.ToList()) // ToList 安全迭代
                {
                    try
                    {
                        action?.Invoke(hitEventData);
                    }
                    catch (System.Exception e)
                    {
                        Log($"执行触发器命中动作时出错: {e.Message}", "Skill", GameLogManager.LogType.Error);
                    }
                }
            }
        }
    }

    private void ExecuteTriggerStopActions()
    {
        if (_triggerStopActions.Count > 0)
        {
            Log($"执行触发器注册的停止动作，触发器数量: {_triggerStopActions.Count}", "Skill");
            foreach (var kvp in _triggerStopActions.ToList()) // ToList 安全迭代
            {
                string triggerId = kvp.Key;
                var actionsList = kvp.Value;
            
                if (actionsList == null || actionsList.Count == 0) continue;
            
                Log($"执行触发器 {triggerId} 的停止动作，数量: {actionsList.Count}", "Skill");
                foreach (var action in actionsList.ToList()) // ToList 安全迭代
                {
                    try
                    {
                        action?.Invoke();
                    }
                    catch (System.Exception e)
                    {
                        Log($"执行触发器 {triggerId} 停止动作时出错: {e.Message}", "Skill", GameLogManager.LogType.Error);
                    }
                }
            }
        }
    }
    #endregion

}
