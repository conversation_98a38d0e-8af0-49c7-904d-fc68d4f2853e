"use strict";var e=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(exports,"__esModule",{value:!0}),exports.GenTSModule=void 0;const O=e(require("cli-color")),E=e(require("path")),q=require("../utils/IOUtils"),F=require("../utils/StrUtils"),B=require("../DataModel"),j=require("../utils/CommonUtils"),b=require("../TSTypeEnum"),W=require("../utils/LineBreak"),A=require("../utils/CodeWriter");class t{static get Instance(){return null==this._instance&&(this._instance=new t),this._instance}gen(t){this._export=B.DataModel.Instance.config.exports.find(e=>e.id==t),this._codeLang=this._export.code_language,console.log(`
================================= 开始生成 ${this._export.id} 配置 =================================
`),q.IOUtils.deleteFile(this._export.export_url),q.IOUtils.deleteFolderFile(this._export.export_script_url,!1),q.IOUtils.makeDir(this._export.export_script_url),q.IOUtils.copy(`templates/${this._export.template_name||this._export.id}/scripts/`,this._export.export_script_url),this._configNames=B.DataModel.Instance.getConfigNamesAndCutDataByConfigType(t),this._configSplitor=B.DataModel.Instance.config.export_data_splitor,B.DataModel.Instance.config.export_data_splitor_random_enabled&&(this._configSplitor=F.StrUtils.genPassword(8,!0,!0,!1,!0));let e=this.genEnum();return e=(e=(e=e&&this.genItemAndVertical())&&this.genMgr())&&this.genConfigText()}genEnum(){var t=j.CommonUtils.getTemplate(this._export,"ConfigEnum.txt");let a=Object.keys(B.DataModel.Instance.enum);a=a.filter(e=>{var e=B.DataModel.Instance.remark[e];return 0<=(null==(e=null==e?void 0:e.generate)?void 0:e.indexOf(this._export.id))});for(let e=0;e<a.length;e++){var o=a[e];let n=B.DataModel.Instance.enum[o],i=new A.CodeWriter;n.forEach((e,t)=>{e.annotation&&i.addStr(j.CommonUtils.getCommentStr(this._codeLang,e.annotation,1)+"\n"),j.CommonUtils.numIsInt(+e.value)?i.add(1,e.key+" = "+e.value,!1):i.add(1,`${e.key} = "${e.value}"`,!1),t<n.length-1&&i.add(0,",")});var r=F.StrUtils.format(t,o,i.content);q.IOUtils.writeTextFile(E.default.join(this._export.export_script_url,o+"."+this._export.script_suffix),r,W.LineBreak.CRLF,"导出枚举类脚本成功！-> {0}")}return!0}genItemAndVertical(){var t=j.CommonUtils.getTemplate(this._export,"ConfigItem.txt"),n=j.CommonUtils.getTemplate(this._export,"ConfigSingle.txt");for(let e=0;e<this._configNames.length;e++){var a=this._configNames[e],o=B.DataModel.Instance.originConfig[a],r=B.DataModel.Instance.remark[a],s=r.parent&&B.DataModel.Instance.remark[r.parent],l=s&&r.parent+B.DataModel.Instance.config.export_item_suffix;if(o.fixed_keys){var d=B.DataModel.Instance.getConfigUniqueKeyType(a,this._codeLang),f=a+B.DataModel.Instance.config.export_item_suffix;let e="",n=new A.CodeWriter,i=new A.CodeWriter;if(s&&(n.add(0,`import { ${l} } from "./${l}";`),e=" extends "+l),!r.parent&&(i.addStr(j.CommonUtils.getCommentStr(this._codeLang,"唯一Key",1)+"\n"),i.add(1,`readonly uniqueKey: ${d};`),!r.isSingleMainKey)){var m=r.mainKeyNames;for(let e=0;e<m.length;e++){var g=m[e],g=B.DataModel.Instance.getConfigKeyType(a,g,this._codeLang);i.addStr(j.CommonUtils.getCommentStr(this._codeLang,`第${e+1}主键`,1)+"\n"),i.add(1,`readonly ${B.DataModel.Instance.getMainKeyVarName(e+1)}: ${g};`)}}for(let t=0;t<o.fixed_keys.length;t++){var c=o.fixed_keys[t];if(!(s&&null!=s&&s.fields&&s.fields[c])){var p=F.StrUtils.convertToLowerCamelCase(c),_=(null==r?void 0:r.fields)&&r.fields[c];null!=_&&_.annotation&&i.addStr(j.CommonUtils.getCommentStr(this._codeLang,_.annotation,1)+"\n");let e=B.DataModel.Instance.getConfigKeyType(a,c,this._codeLang);if(null!=_&&_.enum){if(e!=b.TSTypeEnum.Int&&e!=b.TSTypeEnum.String)return console.log(O.default.red("枚举的值不是整数或字符串！-> "+e+" "+b.TSTypeEnum.Int+" "+a+" -> "+c)),!1;e=_.enum,i.add(1,`readonly ${p}: ${e};`,!1);var u=`import { ${e} } from "./${e}";`;-1==n.content.indexOf(u)&&n.add(0,u)}else if(null!=_&&_.link){u=_.link+B.DataModel.Instance.config.export_item_suffix;if(_.linkIsArray){if(e!=b.TSTypeEnum.IntList&&e!=b.TSTypeEnum.StringList)return console.log(O.default.red("链接的值不是整数数组或字符串数组！-> "+a+" -> "+c)),!1;i.add(1,`readonly ${p}: ${u}[];`,!1)}else i.add(1,`readonly ${p}: ${u};`,!1);_=`import { ${u} } from "./${u}";`;-1==n.content.indexOf(_)&&n.add(0,_)}else i.add(1,`readonly ${p}: ${e};`,!1);t<o.fixed_keys.length-1&&i.newLine()}}""!=n.content&&n.newLine();l=F.StrUtils.format(t,n.content,f+e,i.content);q.IOUtils.writeTextFile(E.default.join(this._export.export_script_url,a+B.DataModel.Instance.config.export_item_suffix+"."+this._export.script_suffix),l,W.LineBreak.CRLF,`导出配置Item(${r.sheetType})脚本成功！-> {0}`)}else{let e=new A.CodeWriter;var i=Object.keys(o).length;let t=0;for(const $ in o){var h=B.DataModel.Instance.getConfigKeyType(a,$,this._codeLang),x=r.fields&&r.fields[$],x=(null!=x&&x.annotation&&e.addStr(j.CommonUtils.getCommentStr(this._codeLang,x.annotation,1)+"\n"),F.StrUtils.convertToLowerCamelCase($));e.add(1,`readonly ${x}: ${h};`,!1),t<i-1&&e.newLine(),t++}d=F.StrUtils.format(n,"",a,e.content);q.IOUtils.writeTextFile(E.default.join(this._export.export_script_url,a+"."+this._export.script_suffix),d,W.LineBreak.CRLF,`导出配置(${r.sheetType})脚本成功！-> {0}`)}}return!0}genMgr(){var e=j.CommonUtils.getTemplate(this._export,"ConfigMgr.txt");let r=new A.CodeWriter,s=new A.CodeWriter,l=new A.CodeWriter;var d=j.CommonUtils.getTemplate(this._export,"ConfigVar.txt"),f=j.CommonUtils.getTemplate(this._export,"ConfigItemVar.txt");for(let e=0;e<this._configNames.length;e++){var m=this._configNames[e],g=B.DataModel.Instance.originConfig[m],c=B.DataModel.Instance.remark[m];let a=B.DataModel.Instance.getParents(m);var p=a?a.length:0,_=m+B.DataModel.Instance.config.export_item_suffix,u=F.StrUtils.convertToLowerCamelCase(m);l.add(2,"// "+m),l.add(2,`section = sections[${e}];`);let o=g.fixed_keys;if(o){r.add(0,`import { ${_} } from "./${_}";`);var h,x=B.DataModel.Instance.getConfigUniqueKeyType(m,this._codeLang);B.DataModel.Instance.isConventionType(x,this._codeLang)||(h=`import { ${x} } from "./${x}";`,-1==r.content.indexOf(h)&&r.add(0,h));let t=1;c.isSingleMainKey||(h=c.mainKeyNames,t=h.length+1),s.add(0,F.StrUtils.format(d,"_"+u,x,_,m,x,_,"_"+u)),l.add(2,"totalLength = section.length;"),l.add(2,`nAdd = ${o.length+t};`);let n;p?(n=F.StrUtils.convertToLowerCamelCase(a[a.length-1],!0),l.add(2,`let map${e} = this.${n};`),l.add(2,`let map${e}_self = new Map<${x}, ${_}>();`)):l.add(2,`let map${e} = new Map<${x}, ${_}>();`),l.add(2,"for (let n = 0; n < totalLength; n += nAdd) {"),p?(a.forEach((e,t)=>{e+=B.DataModel.Instance.config.export_item_suffix;l.add(3,`let parentItem${t+1} = this.${n}.get(section[n]) as ${e};`)}),l.add(3,`let item: ${_} = { uniqueKey: parentItem${p}.uniqueKey, `,!1)):l.add(3,`let item: ${_} = { uniqueKey: section[n], `,!1);var $=Object.keys(g.data)[0];isNaN(+$)?b.TSTypeEnum.String:b.TSTypeEnum.Int;if(!c.isSingleMainKey){var y=c.mainKeyNames;for(let e=0;e<y.length;e++){var C=B.DataModel.Instance.getMainKeyVarName(e+1);p?l.add(0,C+`: parentItem${p}.${C}, `,!1):l.add(0,`${C}: section[n + ${e+1}], `,!1)}}if(p){let o=[];a.forEach((e,t)=>{var n=B.DataModel.Instance.originConfig[e].fixed_keys;for(let e=0;e<n.length;e++){var i,a=n[e];-1==o.indexOf(a)&&(i=F.StrUtils.convertToLowerCamelCase(a),l.add(0,i+`: parentItem${t+1}.${i}, `,!1),o.push(a))}})}let i;for(let e=0;e<o.length;e++){var I=o[e];let n=!1;if(p)for(let t=a.length-1;!n&&0<=t;t--){var S=a[t];let e=B.DataModel.Instance.originConfig[S].fixed_keys;-1!=e.indexOf(I)&&(n=!0)}if(!n){var T=B.DataModel.Instance.getConfigKeyType(m,I,this._codeLang),v=(null==c?void 0:c.fields)&&c.fields[I];if(null!=v&&v.enum){if(T!=b.TSTypeEnum.Int&&T!=b.TSTypeEnum.String)return console.log(O.default.red(`枚举的数值不是整数也不是字符串，这是不被允许的! 表名：${m}，字段：${I}，文件路径：`+c.filePath)),!1;l.add(0,`${F.StrUtils.convertToLowerCamelCase(I)}: section[n + ${e+t}]`,!1)}else if(null!=v&&v.link){var M=v.link,M=F.StrUtils.convertToLowerCamelCase(M);if(v.linkIsArray){if(T!=b.TSTypeEnum.IntList&&T!=b.TSTypeEnum.StringList)return console.log(O.default.red(`链接的值不是整数数组或字符串数组！表名：${m}，字段：${I}，文件路径：`+c.filePath)),console.log(T),!1;var T=B.DataModel.Instance.getConfigUniqueKeyType(v.link,this._codeLang),L=v.link+B.DataModel.Instance.config.export_item_suffix;l.add(0,`${F.StrUtils.convertToLowerCamelCase(I)}: this.getLinkedConfigs<${T}, ${L}>(section[n + ${e+t}], this.${v.link})`,!1)}else l.add(0,`${F.StrUtils.convertToLowerCamelCase(I)}: this._${M}.get(section[n + ${e+t}])`,!1)}else l.add(0,`${F.StrUtils.convertToLowerCamelCase(I)}: section[n + ${e+t}]`,!1);e==o.length-1?l.add(0," };"):l.add(0,", ",!1),i=!0}}if(i||l.add(0," };"),p?(l.add(3,`map${e}.data.set(item.uniqueKey, item);`),l.add(3,`map${e}_self.set(item.uniqueKey, item);`)):l.add(3,`map${e}.set(item.uniqueKey, item);`),l.add(2,"}"),p?l.add(2,`this._${u} = new BaseConfig<${x}, ${_}>("${m}", map${e}_self);`):l.add(2,`this._${u} = new BaseConfig<${x}, ${_}>("${m}", map${e});`),!c.isSingleMainKey){s.newLine();var U=c.mainKeyNames,$=B.DataModel.Instance.getConfigCollectionTypeByIndex(m,this._codeLang),D="_"+u+B.DataModel.Instance.config.export_collection_suffix;s.add(0,F.StrUtils.format(f,D,$,m+B.DataModel.Instance.config.export_collection_suffix,$,D)),l.newLine(),l.add(2,`this.${D} = new ${$}();`),l.add(2,`this._${u}.data.forEach(item => {`);for(let n=1;n<U.length;n++){l.add(3,"if (!this."+D,!1);let t=n;for(let e=0;e<t;e++){var w=e+1;e!=t-1?l.add(0,`.get(item.mainKey${w})`,!1):l.add(0,`.has(item.mainKey${w})`,!1)}l.add(0,")"),l.add(4,"this."+D,!1),t=n;for(let e=0;e<t;e++){var k,K=e+1;e!=t-1?l.add(0,`.get(item.mainKey${K})`,!1):(k=B.DataModel.Instance.getConfigCollectionTypeByIndex(m,this._codeLang,n,U.length-1),l.add(0,`.set(item.mainKey${K}, new ${k}());`))}}l.add(3,"this."+D,!1);for(let e=0;e<U.length;e++){var N=e+1;e!=U.length-1?l.add(0,`.get(item.mainKey${N})`,!1):l.add(0,`.set(item.mainKey${N}, item);`)}l.add(2,"});")}}else{x=`import { ${m} } from "./${m}";`;-1==r.content.indexOf(x)&&r.add(0,x),s.add(0,F.StrUtils.format(f,"_"+u,m,m,m,"_"+u)),o=Object.keys(g),l.add(2,`this._${u} = { configName: "${m}", `,!1),o.forEach((e,t)=>{l.addStr(F.StrUtils.convertToLowerCamelCase(e)+`: section[${t}]`),t==o.length-1?l.add(0," };"):l.addStr(", ")})}e<this._configNames.length-1&&(s.newLine(),l.newLine())}e=F.StrUtils.format(e,r.content,s.content,this._configSplitor,l.content);return q.IOUtils.writeTextFile(E.default.join(this._export.export_script_url,this._export.export_config_manager_name+"."+this._export.script_suffix),e,W.LineBreak.CRLF,`成功导出配置管理类脚本（${this._export.export_config_manager_name}）-> {0}`),!0}genConfigText(){let n=[];for(let e=0;e<this._configNames.length;e++){var i=this._configNames[e],t=B.DataModel.Instance.originConfig[i],a=t.fixed_keys;if(0<e&&n.push(this._configSplitor),a){var o=t.data;for(const f in o){var r=o[f];let e=isNaN(+f)?f:+f,t=(n.push(e),isNaN(+f)&&e.split("_"));var s=B.DataModel.Instance.remark[i];t&&1<t.length&&s.mainKeySubs&&t.length==s.mainKeySubs.length&&t.forEach(e=>{e=isNaN(+e)?e:+e,n.push(e)});for(let e=0;e<r.length;e++){var l=r[e];n.push(l)}}}else for(const m in t){var d=t[m];n.push(d)}}return q.IOUtils.writeTextFile(this._export.export_url,JSON.stringify(n),W.LineBreak.CRLF,"导出配置文本成功！-> {0}"),!0}}exports.GenTSModule=t;