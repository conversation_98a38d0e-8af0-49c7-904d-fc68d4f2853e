"use strict";var e=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(exports,"__esModule",{value:!0});const t=require("./DataModel"),l=require("./genModules/GenOriginModule"),i=e(require("cli-color")),o=e(require("uglify-js")),r=require("./genModules/GenTSModule"),n=require("./genModules/GenCSModule"),d=require("./utils/IOUtils"),a=e(require("fs")),s=e(require("path")),u=require("./CodeLang"),c=require("./genModules/GenETCSModule");t.DataModel.Instance.reset();let f=l.GenOriginModule.Instance.gen();if(f){let l=t.DataModel.Instance.config.exports;for(let e=0;e<l.length;e++){const M=l[e];if(M.enabled){let e=s.default.dirname(M.export_url);if(!d.IOUtils.fileOrFolderIsExsit(M.export_script_url)){if(!M.force_make_dir){console.log(i.default.yellow(M.id+" 发布失败，路径不存在："+M.export_script_url));continue}d.IOUtils.makeDir(M.export_script_url)}if(!d.IOUtils.fileOrFolderIsExsit(e)){if(!M.force_make_dir){console.log(i.default.yellow(M.id+" 发布失败，路径不存在："+e));continue}d.IOUtils.makeDir(e)}switch(t.DataModel.Instance.reset(),M.code_language){case u.CodeLang.CS:f=n.GenCSModule.Instance.gen(M.id);break;case u.CodeLang.TS:f=r.GenTSModule.Instance.gen(M.id);break;case u.CodeLang.ETCS:f=c.GenETCSModule.Instance.gen(M.id);break;default:console.log(i.default.red(M.id+" 发布失败，路径不存在："+e))}if(!f)break}}}let g=[];d.IOUtils.findFile("dist",[".js",".JS"],g);for(let r=g.length-1;0<=r;r--){let e=g[r],l=a.default.readFileSync(e,{encoding:"utf-8"}),t={mangle:{toplevel:!0}},i=o.default.minify(l,t);if(l==i.code)break;d.IOUtils.writeTextFile(e,i.code)}f?console.log(i.default.green("\n<<< 所有配置发布完成！>>>")):console.log(i.default.yellow("发布配置失败，发布配置过程中出现错误！请向上翻看是否有报错信息。"));