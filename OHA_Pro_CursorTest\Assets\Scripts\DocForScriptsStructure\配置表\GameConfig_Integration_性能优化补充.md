# GameConfig系统性能优化与扩展补充文档

本文档是《GameConfig_Integration_技术文档.md》的补充，专门介绍性能优化、扩展功能和高级用法。

## 1. 配置系统性能优化

### 1.1 配置数据缓存策略

```csharp
/// <summary>
/// 配置数据缓存管理器，提供高效的配置数据访问
/// </summary>
public class ConfigCacheManager : MonoBehaviour
{
    private static ConfigCacheManager _instance;
    public static ConfigCacheManager Instance
    {
        get
        {
            if (_instance == null)
            {
                var go = new GameObject("ConfigCacheManager");
                _instance = go.AddComponent<ConfigCacheManager>();
                DontDestroyOnLoad(go);
            }
            return _instance;
        }
    }

    [Header("缓存设置")]
    public int maxCacheSize = 1000;
    public float cacheExpireTime = 300f; // 5分钟

    // 解析结果缓存
    private Dictionary<string, CachedActionConfig> actionConfigCache = new Dictionary<string, CachedActionConfig>();
    private Dictionary<string, CachedTriggerConfig> triggerConfigCache = new Dictionary<string, CachedTriggerConfig>();
    private Dictionary<string, CachedConditionResult> conditionResultCache = new Dictionary<string, CachedConditionResult>();

    // LRU缓存实现
    private LinkedList<string> accessOrder = new LinkedList<string>();
    private Dictionary<string, LinkedListNode<string>> accessNodes = new Dictionary<string, LinkedListNode<string>>();

    private void Start()
    {
        // 定期清理过期缓存
        InvokeRepeating(nameof(CleanExpiredCache), cacheExpireTime, cacheExpireTime);
    }

    /// <summary>
    /// 获取缓存的动作配置
    /// </summary>
    public List<IAPIAction> GetCachedActionConfig(string actionConfig)
    {
        if (string.IsNullOrEmpty(actionConfig))
            return new List<IAPIAction>();

        string cacheKey = $"action_{actionConfig.GetHashCode()}";

        if (actionConfigCache.TryGetValue(cacheKey, out CachedActionConfig cached))
        {
            if (Time.time - cached.CacheTime < cacheExpireTime)
            {
                UpdateAccessOrder(cacheKey);
                return cached.Actions;
            }
            else
            {
                // 缓存过期，移除
                RemoveFromCache(cacheKey);
            }
        }

        // 解析并缓存
        var actions = ActionConfigParser.ParseActionConfig(actionConfig);
        CacheActionConfig(cacheKey, actions);
        return actions;
    }

    /// <summary>
    /// 获取缓存的条件评估结果
    /// </summary>
    public bool? GetCachedConditionResult(string condition, LogicExecutionContext context)
    {
        if (string.IsNullOrEmpty(condition))
            return true;

        // 生成上下文相关的缓存键
        string contextKey = GenerateContextKey(context);
        string cacheKey = $"condition_{condition.GetHashCode()}_{contextKey.GetHashCode()}";

        if (conditionResultCache.TryGetValue(cacheKey, out CachedConditionResult cached))
        {
            if (Time.time - cached.CacheTime < 1f) // 条件结果缓存时间较短
            {
                UpdateAccessOrder(cacheKey);
                return cached.Result;
            }
            else
            {
                RemoveFromCache(cacheKey);
            }
        }

        return null; // 缓存未命中
    }

    /// <summary>
    /// 缓存条件评估结果
    /// </summary>
    public void CacheConditionResult(string condition, LogicExecutionContext context, bool result)
    {
        string contextKey = GenerateContextKey(context);
        string cacheKey = $"condition_{condition.GetHashCode()}_{contextKey.GetHashCode()}";

        var cached = new CachedConditionResult
        {
            Result = result,
            CacheTime = Time.time
        };

        conditionResultCache[cacheKey] = cached;
        UpdateAccessOrder(cacheKey);
        EnforceCacheLimit();
    }

    private string GenerateContextKey(LogicExecutionContext context)
    {
        // 生成上下文相关的键，用于条件缓存
        var sb = new System.Text.StringBuilder();
        
        if (context.Target != null)
        {
            sb.Append($"target_{context.Target.GetInstanceID()}");
            // 添加关键属性值
            var healthAttr = context.Target.GetAttribute("Health_Attribute");
            if (healthAttr != null)
                sb.Append($"_h{healthAttr.Value:F1}");
        }

        if (context.Caster != null)
        {
            sb.Append($"_caster_{context.Caster.GetInstanceID()}");
        }

        sb.Append($"_layer{context.Layer}");

        return sb.ToString();
    }

    private void UpdateAccessOrder(string cacheKey)
    {
        if (accessNodes.TryGetValue(cacheKey, out LinkedListNode<string> node))
        {
            accessOrder.Remove(node);
        }

        var newNode = accessOrder.AddFirst(cacheKey);
        accessNodes[cacheKey] = newNode;
    }

    private void EnforceCacheLimit()
    {
        while (accessOrder.Count > maxCacheSize)
        {
            var lastKey = accessOrder.Last.Value;
            RemoveFromCache(lastKey);
        }
    }

    private void RemoveFromCache(string cacheKey)
    {
        actionConfigCache.Remove(cacheKey);
        triggerConfigCache.Remove(cacheKey);
        conditionResultCache.Remove(cacheKey);

        if (accessNodes.TryGetValue(cacheKey, out LinkedListNode<string> node))
        {
            accessOrder.Remove(node);
            accessNodes.Remove(cacheKey);
        }
    }

    private void CleanExpiredCache()
    {
        var currentTime = Time.time;
        var keysToRemove = new List<string>();

        // 检查动作配置缓存
        foreach (var kvp in actionConfigCache)
        {
            if (currentTime - kvp.Value.CacheTime > cacheExpireTime)
            {
                keysToRemove.Add(kvp.Key);
            }
        }

        // 检查条件结果缓存（较短的过期时间）
        foreach (var kvp in conditionResultCache)
        {
            if (currentTime - kvp.Value.CacheTime > 1f)
            {
                keysToRemove.Add(kvp.Key);
            }
        }

        // 移除过期缓存
        foreach (var key in keysToRemove)
        {
            RemoveFromCache(key);
        }

        if (keysToRemove.Count > 0)
        {
            GameLogManager.Log($"清理了 {keysToRemove.Count} 个过期缓存项", "ConfigCacheManager");
        }
    }

    /// <summary>
    /// 清空所有缓存
    /// </summary>
    public void ClearAllCache()
    {
        actionConfigCache.Clear();
        triggerConfigCache.Clear();
        conditionResultCache.Clear();
        accessOrder.Clear();
        accessNodes.Clear();
        
        GameLogManager.Log("已清空所有配置缓存", "ConfigCacheManager");
    }

    /// <summary>
    /// 获取缓存统计信息
    /// </summary>
    public CacheStatistics GetCacheStatistics()
    {
        return new CacheStatistics
        {
            ActionConfigCacheCount = actionConfigCache.Count,
            TriggerConfigCacheCount = triggerConfigCache.Count,
            ConditionResultCacheCount = conditionResultCache.Count,
            TotalCacheCount = accessOrder.Count,
            MaxCacheSize = maxCacheSize
        };
    }

    private void CacheActionConfig(string cacheKey, List<IAPIAction> actions)
    {
        var cached = new CachedActionConfig
        {
            Actions = actions,
            CacheTime = Time.time
        };

        actionConfigCache[cacheKey] = cached;
        UpdateAccessOrder(cacheKey);
        EnforceCacheLimit();
    }
}

// 缓存数据结构
public class CachedActionConfig
{
    public List<IAPIAction> Actions;
    public float CacheTime;
}

public class CachedTriggerConfig
{
    public ITriggerConfig TriggerConfig;
    public float CacheTime;
}

public class CachedConditionResult
{
    public bool Result;
    public float CacheTime;
}

public class CacheStatistics
{
    public int ActionConfigCacheCount;
    public int TriggerConfigCacheCount;
    public int ConditionResultCacheCount;
    public int TotalCacheCount;
    public int MaxCacheSize;

    public float CacheUsagePercentage => (float)TotalCacheCount / MaxCacheSize * 100f;
}
```

### 1.2 性能监控系统

```csharp
/// <summary>
/// 配置系统性能监控器
/// </summary>
public class ConfigPerformanceMonitor : MonoBehaviour
{
    private static ConfigPerformanceMonitor _instance;
    public static ConfigPerformanceMonitor Instance
    {
        get
        {
            if (_instance == null)
            {
                var go = new GameObject("ConfigPerformanceMonitor");
                _instance = go.AddComponent<ConfigPerformanceMonitor>();
                DontDestroyOnLoad(go);
            }
            return _instance;
        }
    }

    [Header("监控设置")]
    public bool enableMonitoring = true;
    public float reportInterval = 10f;
    public int maxRecordCount = 1000;

    // 性能记录
    private List<PerformanceRecord> records = new List<PerformanceRecord>();
    private Dictionary<string, PerformanceStats> operationStats = new Dictionary<string, PerformanceStats>();

    private void Start()
    {
        if (enableMonitoring)
        {
            InvokeRepeating(nameof(GeneratePerformanceReport), reportInterval, reportInterval);
        }
    }

    /// <summary>
    /// 记录操作性能
    /// </summary>
    public void RecordOperation(string operationType, string operationName, float executionTime, bool success = true)
    {
        if (!enableMonitoring) return;

        var record = new PerformanceRecord
        {
            OperationType = operationType,
            OperationName = operationName,
            ExecutionTime = executionTime,
            Success = success,
            Timestamp = Time.time
        };

        records.Add(record);

        // 更新统计信息
        string statsKey = $"{operationType}_{operationName}";
        if (!operationStats.TryGetValue(statsKey, out PerformanceStats stats))
        {
            stats = new PerformanceStats { OperationType = operationType, OperationName = operationName };
            operationStats[statsKey] = stats;
        }

        stats.TotalExecutions++;
        stats.TotalExecutionTime += executionTime;
        stats.AverageExecutionTime = stats.TotalExecutionTime / stats.TotalExecutions;

        if (executionTime > stats.MaxExecutionTime)
        {
            stats.MaxExecutionTime = executionTime;
        }

        if (stats.MinExecutionTime == 0 || executionTime < stats.MinExecutionTime)
        {
            stats.MinExecutionTime = executionTime;
        }

        if (success)
        {
            stats.SuccessCount++;
        }
        else
        {
            stats.FailureCount++;
        }

        // 限制记录数量
        if (records.Count > maxRecordCount)
        {
            records.RemoveRange(0, records.Count - maxRecordCount);
        }
    }

    /// <summary>
    /// 生成性能报告
    /// </summary>
    private void GeneratePerformanceReport()
    {
        if (operationStats.Count == 0) return;

        var report = new System.Text.StringBuilder();
        report.AppendLine("=== 配置系统性能报告 ===");
        report.AppendLine($"报告时间: {System.DateTime.Now:yyyy-MM-dd HH:mm:ss}");
        report.AppendLine($"监控间隔: {reportInterval}秒");
        report.AppendLine();

        // 按操作类型分组统计
        var groupedStats = operationStats.Values.GroupBy(s => s.OperationType);

        foreach (var group in groupedStats)
        {
            report.AppendLine($"操作类型: {group.Key}");
            report.AppendLine("----------------------------------------");

            foreach (var stats in group.OrderByDescending(s => s.TotalExecutions))
            {
                report.AppendLine($"  操作: {stats.OperationName}");
                report.AppendLine($"    执行次数: {stats.TotalExecutions}");
                report.AppendLine($"    成功率: {(stats.SuccessCount / (float)stats.TotalExecutions * 100):F1}%");
                report.AppendLine($"    平均耗时: {stats.AverageExecutionTime * 1000:F2}ms");
                report.AppendLine($"    最大耗时: {stats.MaxExecutionTime * 1000:F2}ms");
                report.AppendLine($"    最小耗时: {stats.MinExecutionTime * 1000:F2}ms");
                report.AppendLine();
            }
        }

        // 缓存统计
        var cacheStats = ConfigCacheManager.Instance.GetCacheStatistics();
        report.AppendLine("缓存统计:");
        report.AppendLine($"  动作配置缓存: {cacheStats.ActionConfigCacheCount}");
        report.AppendLine($"  触发器配置缓存: {cacheStats.TriggerConfigCacheCount}");
        report.AppendLine($"  条件结果缓存: {cacheStats.ConditionResultCacheCount}");
        report.AppendLine($"  缓存使用率: {cacheStats.CacheUsagePercentage:F1}%");

        GameLogManager.Log(report.ToString(), "ConfigPerformanceMonitor");
    }

    /// <summary>
    /// 获取性能统计信息
    /// </summary>
    public Dictionary<string, PerformanceStats> GetPerformanceStats()
    {
        return new Dictionary<string, PerformanceStats>(operationStats);
    }

    /// <summary>
    /// 清空性能记录
    /// </summary>
    public void ClearPerformanceRecords()
    {
        records.Clear();
        operationStats.Clear();
        GameLogManager.Log("已清空性能记录", "ConfigPerformanceMonitor");
    }
}

// 性能记录数据结构
public class PerformanceRecord
{
    public string OperationType;
    public string OperationName;
    public float ExecutionTime;
    public bool Success;
    public float Timestamp;
}

public class PerformanceStats
{
    public string OperationType;
    public string OperationName;
    public int TotalExecutions;
    public int SuccessCount;
    public int FailureCount;
    public float TotalExecutionTime;
    public float AverageExecutionTime;
    public float MaxExecutionTime;
    public float MinExecutionTime;
}
```

## 2. 配置系统扩展指南

### 2.1 添加新的API动作类型

```csharp
/// <summary>
/// 自定义API动作示例：传送单位
/// 配置格式：TeleportUnit:target=self,position=10,0,5,playEffect=true
/// </summary>
public class TeleportUnitAPIAction : APIActionBase
{
    private string targetType;
    private Vector3 position;
    private bool playEffect;

    public override void Initialize(Dictionary<string, string> parameters)
    {
        base.Initialize(parameters);
        
        targetType = GetStringParameter("target", "self");
        playEffect = GetBoolParameter("playEffect", true);
        
        // 解析位置参数
        string positionStr = GetStringParameter("position", "0,0,0");
        position = ParseVector3(positionStr);
    }

    public override void Execute(APIExecutionContext context)
    {
        var targetUnit = GetTargetUnit(targetType, context);
        if (targetUnit == null)
        {
            GameLogManager.Log($"TeleportUnit动作找不到目标单位: {targetType}", 
                             "TeleportUnitAPIAction", GameLogManager.LogType.Warning);
            return;
        }

        // 执行传送
        targetUnit.transform.position = position;

        // 播放传送特效
        if (playEffect)
        {
            PlayTeleportEffect(targetUnit.transform.position);
        }

        GameLogManager.Log($"传送单位 {targetUnit.name} 到位置 {position}", "TeleportUnitAPIAction");
    }

    private void PlayTeleportEffect(Vector3 position)
    {
        var effectPrefab = Resources.Load<GameObject>("FX/TeleportEffect");
        if (effectPrefab != null)
        {
            var effect = Object.Instantiate(effectPrefab, position, Quaternion.identity);
            Object.Destroy(effect, 2f);
        }
    }

    private Vector3 ParseVector3(string vectorStr)
    {
        var parts = vectorStr.Split(',');
        if (parts.Length == 3)
        {
            if (float.TryParse(parts[0], out float x) &&
                float.TryParse(parts[1], out float y) &&
                float.TryParse(parts[2], out float z))
            {
                return new Vector3(x, y, z);
            }
        }
        return Vector3.zero;
    }

    public override bool ValidateParameters()
    {
        return base.ValidateParameters() && !string.IsNullOrEmpty(targetType);
    }
}

/// <summary>
/// 生成单位的API动作
/// 配置格式：SpawnUnit:unitId=goblin_warrior,position=5,0,3,count=3
/// </summary>
public class SpawnUnitAPIAction : APIActionBase
{
    private string unitId;
    private Vector3 spawnPosition;
    private int count;

    public override void Initialize(Dictionary<string, string> parameters)
    {
        base.Initialize(parameters);
        
        unitId = GetStringParameter("unitId");
        count = GetIntParameter("count", 1);
        
        string positionStr = GetStringParameter("position", "0,0,0");
        spawnPosition = ParseVector3(positionStr);
    }

    public override void Execute(APIExecutionContext context)
    {
        if (string.IsNullOrEmpty(unitId))
        {
            GameLogManager.Log("SpawnUnit动作缺少unitId参数", "SpawnUnitAPIAction", GameLogManager.LogType.Warning);
            return;
        }

        for (int i = 0; i < count; i++)
        {
            SpawnSingleUnit();
        }
    }

    private void SpawnSingleUnit()
    {
        // 从配置中获取单位预制体路径
        var monsterConfig = ConfigMgr.MonsterConfig.Get(unitId);
        if (monsterConfig == null)
        {
            GameLogManager.Log($"找不到单位配置: {unitId}", "SpawnUnitAPIAction", GameLogManager.LogType.Warning);
            return;
        }

        // 加载并实例化单位预制体
        var prefab = Resources.Load<GameObject>(monsterConfig.PrefabPath);
        if (prefab != null)
        {
            var spawnPos = spawnPosition + UnityEngine.Random.insideUnitSphere * 2f;
            spawnPos.y = spawnPosition.y; // 保持Y轴位置

            var instance = Object.Instantiate(prefab, spawnPos, Quaternion.identity);
            
            // 应用单位配置
            var unit = instance.GetComponent<Unit>();
            if (unit != null)
            {
                UnitConfigAdapter.ApplyMonsterConfig(unit, unitId);
            }

            GameLogManager.Log($"生成单位: {unitId} 在位置 {spawnPos}", "SpawnUnitAPIAction");
        }
        else
        {
            GameLogManager.Log($"找不到单位预制体: {monsterConfig.PrefabPath}", "SpawnUnitAPIAction", GameLogManager.LogType.Warning);
        }
    }

    private Vector3 ParseVector3(string vectorStr)
    {
        var parts = vectorStr.Split(',');
        if (parts.Length == 3)
        {
            if (float.TryParse(parts[0], out float x) &&
                float.TryParse(parts[1], out float y) &&
                float.TryParse(parts[2], out float z))
            {
                return new Vector3(x, y, z);
            }
        }
        return Vector3.zero;
    }

    public override bool ValidateParameters()
    {
        return base.ValidateParameters() && !string.IsNullOrEmpty(unitId) && count > 0;
    }
}
```

### 2.2 添加新的触发器类型

```csharp
/// <summary>
/// 距离触发器配置
/// 配置格式：DistanceTrigger:distance=5.0,target=enemy,action=AddBuff
/// </summary>
public class DistanceTriggerConfig : ITriggerConfig
{
    private float triggerDistance;
    private string targetType;
    private string action;

    public void Initialize(Dictionary<string, string> parameters)
    {
        triggerDistance = GetFloatParameter(parameters, "distance", 5f);
        targetType = GetStringParameter(parameters, "target", "enemy");
        action = GetStringParameter(parameters, "action", "");
    }

    public void CreateTrigger(Unit targetUnit, object source = null)
    {
        // 创建距离触发器组件
        var triggerGO = new GameObject($"DistanceTrigger_{targetUnit.name}");
        triggerGO.transform.SetParent(targetUnit.transform);
        
        var distanceTrigger = triggerGO.AddComponent<DistanceTriggerComponent>();
        distanceTrigger.Initialize(targetUnit, triggerDistance, targetType, action);

        GameLogManager.Log($"创建距离触发器: 距离={triggerDistance}, 目标={targetType}", "DistanceTriggerConfig");
    }

    public bool ValidateParameters()
    {
        return triggerDistance > 0;
    }

    private float GetFloatParameter(Dictionary<string, string> parameters, string key, float defaultValue)
    {
        if (parameters.TryGetValue(key, out string value) && float.TryParse(value, out float result))
        {
            return result;
        }
        return defaultValue;
    }

    private string GetStringParameter(Dictionary<string, string> parameters, string key, string defaultValue)
    {
        return parameters.TryGetValue(key, out string value) ? value : defaultValue;
    }
}

/// <summary>
/// 距离触发器组件
/// </summary>
public class DistanceTriggerComponent : MonoBehaviour
{
    private Unit ownerUnit;
    private float triggerDistance;
    private string targetType;
    private string action;
    private bool hasTriggered = false;

    public void Initialize(Unit owner, float distance, string target, string actionConfig)
    {
        ownerUnit = owner;
        triggerDistance = distance;
        targetType = target;
        action = actionConfig;
    }

    private void Update()
    {
        if (hasTriggered || ownerUnit == null) return;

        // 检查距离条件
        if (CheckDistanceCondition())
        {
            ExecuteTriggerAction();
            hasTriggered = true;
        }
    }

    private bool CheckDistanceCondition()
    {
        // 根据目标类型查找目标单位
        var targets = FindTargetUnits();
        
        foreach (var target in targets)
        {
            float distance = Vector3.Distance(ownerUnit.transform.position, target.transform.position);
            if (distance <= triggerDistance)
            {
                return true;
            }
        }

        return false;
    }

    private List<Unit> FindTargetUnits()
    {
        var targets = new List<Unit>();
        var allUnits = FindObjectsOfType<Unit>();

        foreach (var unit in allUnits)
        {
            if (unit == ownerUnit) continue;

            // 根据目标类型筛选
            if (IsValidTarget(unit))
            {
                targets.Add(unit);
            }
        }

        return targets;
    }

    private bool IsValidTarget(Unit unit)
    {
        switch (targetType.ToLower())
        {
            case "enemy":
                return UnitRelationshipUtil.IsHostile(ownerUnit, unit);
            case "ally":
                return UnitRelationshipUtil.IsFriendly(ownerUnit, unit);
            case "any":
                return true;
            default:
                return false;
        }
    }

    private void ExecuteTriggerAction()
    {
        if (string.IsNullOrEmpty(action)) return;

        var context = new APIExecutionContext
        {
            Caster = ownerUnit.gameObject,
            Target = ownerUnit.gameObject
        };

        var actions = ConfigCacheManager.Instance.GetCachedActionConfig(action);
        foreach (var apiAction in actions)
        {
            apiAction.Execute(context);
        }

        GameLogManager.Log($"距离触发器激活: {ownerUnit.name}", "DistanceTriggerComponent");
    }
}

/// <summary>
/// 时间触发器配置
/// 配置格式：TimeTrigger:delay=3.0,interval=1.0,maxTriggers=5,action=DealDamage
/// </summary>
public class TimeTriggerConfig : ITriggerConfig
{
    private float delay;
    private float interval;
    private int maxTriggers;
    private string action;

    public void Initialize(Dictionary<string, string> parameters)
    {
        delay = GetFloatParameter(parameters, "delay", 0f);
        interval = GetFloatParameter(parameters, "interval", 1f);
        maxTriggers = GetIntParameter(parameters, "maxTriggers", 1);
        action = GetStringParameter(parameters, "action", "");
    }

    public void CreateTrigger(Unit targetUnit, object source = null)
    {
        var triggerGO = new GameObject($"TimeTrigger_{targetUnit.name}");
        triggerGO.transform.SetParent(targetUnit.transform);
        
        var timeTrigger = triggerGO.AddComponent<TimeTriggerComponent>();
        timeTrigger.Initialize(targetUnit, delay, interval, maxTriggers, action);

        GameLogManager.Log($"创建时间触发器: 延迟={delay}s, 间隔={interval}s, 最大次数={maxTriggers}", "TimeTriggerConfig");
    }

    public bool ValidateParameters()
    {
        return interval > 0 && maxTriggers > 0;
    }

    private float GetFloatParameter(Dictionary<string, string> parameters, string key, float defaultValue)
    {
        if (parameters.TryGetValue(key, out string value) && float.TryParse(value, out float result))
        {
            return result;
        }
        return defaultValue;
    }

    private int GetIntParameter(Dictionary<string, string> parameters, string key, int defaultValue)
    {
        if (parameters.TryGetValue(key, out string value) && int.TryParse(value, out int result))
        {
            return result;
        }
        return defaultValue;
    }

    private string GetStringParameter(Dictionary<string, string> parameters, string key, string defaultValue)
    {
        return parameters.TryGetValue(key, out string value) ? value : defaultValue;
    }
}

/// <summary>
/// 时间触发器组件
/// </summary>
public class TimeTriggerComponent : MonoBehaviour
{
    private Unit ownerUnit;
    private float delay;
    private float interval;
    private int maxTriggers;
    private string action;
    private int triggerCount = 0;
    private float nextTriggerTime;

    public void Initialize(Unit owner, float triggerDelay, float triggerInterval, int maxTriggerCount, string actionConfig)
    {
        ownerUnit = owner;
        delay = triggerDelay;
        interval = triggerInterval;
        maxTriggers = maxTriggerCount;
        action = actionConfig;
        
        nextTriggerTime = Time.time + delay;
    }

    private void Update()
    {
        if (triggerCount >= maxTriggers || ownerUnit == null) return;

        if (Time.time >= nextTriggerTime)
        {
            ExecuteTriggerAction();
            triggerCount++;
            nextTriggerTime = Time.time + interval;
        }
    }

    private void ExecuteTriggerAction()
    {
        if (string.IsNullOrEmpty(action)) return;

        var context = new APIExecutionContext
        {
            Caster = ownerUnit.gameObject,
            Target = ownerUnit.gameObject
        };

        var actions = ConfigCacheManager.Instance.GetCachedActionConfig(action);
        foreach (var apiAction in actions)
        {
            apiAction.Execute(context);
        }

        GameLogManager.Log($"时间触发器激活: {ownerUnit.name} (第{triggerCount + 1}次)", "TimeTriggerComponent");

        // 如果达到最大触发次数，销毁触发器
        if (triggerCount >= maxTriggers)
        {
            Destroy(gameObject);
        }
    }
}
```

## 3. Unity编辑器工具扩展

### 3.1 性能监控窗口

```csharp
/// <summary>
/// Unity编辑器性能监控窗口
/// </summary>
public class ConfigPerformanceWindow : EditorWindow
{
    private Vector2 scrollPosition;
    private bool autoRefresh = true;
    private float refreshInterval = 1f;
    private double lastRefreshTime;

    [MenuItem("Tools/GameConfig/性能监控")]
    public static void ShowWindow()
    {
        GetWindow<ConfigPerformanceWindow>("配置性能监控");
    }

    private void OnGUI()
    {
        EditorGUILayout.LabelField("GameConfig性能监控", EditorStyles.boldLabel);
        EditorGUILayout.Space();

        // 控制面板
        DrawControlPanel();
        EditorGUILayout.Space();

        // 性能统计
        DrawPerformanceStats();
        EditorGUILayout.Space();

        // 缓存统计
        DrawCacheStats();

        // 自动刷新
        if (autoRefresh && EditorApplication.timeSinceStartup - lastRefreshTime > refreshInterval)
        {
            lastRefreshTime = EditorApplication.timeSinceStartup;
            Repaint();
        }
    }

    private void DrawControlPanel()
    {
        EditorGUILayout.LabelField("控制面板", EditorStyles.boldLabel);
        
        EditorGUILayout.BeginHorizontal();
        autoRefresh = EditorGUILayout.Toggle("自动刷新", autoRefresh);
        refreshInterval = EditorGUILayout.FloatField("刷新间隔(秒)", refreshInterval);
        EditorGUILayout.EndHorizontal();

        EditorGUILayout.BeginHorizontal();
        if (GUILayout.Button("清空性能记录"))
        {
            if (Application.isPlaying)
            {
                ConfigPerformanceMonitor.Instance.ClearPerformanceRecords();
            }
        }

        if (GUILayout.Button("清空缓存"))
        {
            if (Application.isPlaying)
            {
                ConfigCacheManager.Instance.ClearAllCache();
            }
        }

        if (GUILayout.Button("手动刷新"))
        {
            Repaint();
        }
        EditorGUILayout.EndHorizontal();
    }

    private void DrawPerformanceStats()
    {
        EditorGUILayout.LabelField("性能统计", EditorStyles.boldLabel);

        if (!Application.isPlaying)
        {
            EditorGUILayout.HelpBox("需要在运行时查看性能统计", MessageType.Info);
            return;
        }

        var stats = ConfigPerformanceMonitor.Instance.GetPerformanceStats();
        if (stats.Count == 0)
        {
            EditorGUILayout.LabelField("暂无性能数据");
            return;
        }

        scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);

        // 按操作类型分组显示
        var groupedStats = stats.Values.GroupBy(s => s.OperationType);

        foreach (var group in groupedStats)
        {
            EditorGUILayout.LabelField($"操作类型: {group.Key}", EditorStyles.boldLabel);
            
            foreach (var stat in group.OrderByDescending(s => s.TotalExecutions))
            {
                EditorGUILayout.BeginVertical("box");
                
                EditorGUILayout.LabelField($"操作: {stat.OperationName}", EditorStyles.boldLabel);
                EditorGUILayout.LabelField($"执行次数: {stat.TotalExecutions}");
                EditorGUILayout.LabelField($"成功率: {(stat.SuccessCount / (float)stat.TotalExecutions * 100):F1}%");
                EditorGUILayout.LabelField($"平均耗时: {stat.AverageExecutionTime * 1000:F2}ms");
                EditorGUILayout.LabelField($"最大耗时: {stat.MaxExecutionTime * 1000:F2}ms");
                EditorGUILayout.LabelField($"最小耗时: {stat.MinExecutionTime * 1000:F2}ms");
                
                // 性能警告
                if (stat.AverageExecutionTime > 0.01f) // 超过10ms
                {
                    EditorGUILayout.HelpBox("平均执行时间较长，建议优化", MessageType.Warning);
                }
                
                if (stat.FailureCount > 0)
                {
                    EditorGUILayout.HelpBox($"存在 {stat.FailureCount} 次失败", MessageType.Error);
                }

                EditorGUILayout.EndVertical();
                EditorGUILayout.Space();
            }
        }

        EditorGUILayout.EndScrollView();
    }

    private void DrawCacheStats()
    {
        EditorGUILayout.LabelField("缓存统计", EditorStyles.boldLabel);

        if (!Application.isPlaying)
        {
            EditorGUILayout.HelpBox("需要在运行时查看缓存统计", MessageType.Info);
            return;
        }

        var cacheStats = ConfigCacheManager.Instance.GetCacheStatistics();

        EditorGUILayout.BeginVertical("box");
        EditorGUILayout.LabelField($"动作配置缓存: {cacheStats.ActionConfigCacheCount}");
        EditorGUILayout.LabelField($"触发器配置缓存: {cacheStats.TriggerConfigCacheCount}");
        EditorGUILayout.LabelField($"条件结果缓存: {cacheStats.ConditionResultCacheCount}");
        EditorGUILayout.LabelField($"总缓存数量: {cacheStats.TotalCacheCount} / {cacheStats.MaxCacheSize}");
        
        // 缓存使用率进度条
        var rect = EditorGUILayout.GetControlRect();
        EditorGUI.ProgressBar(rect, cacheStats.CacheUsagePercentage / 100f, $"缓存使用率: {cacheStats.CacheUsagePercentage:F1}%");
        
        // 缓存使用率警告
        if (cacheStats.CacheUsagePercentage > 80f)
        {
            EditorGUILayout.HelpBox("缓存使用率较高，建议增加缓存大小或清理缓存", MessageType.Warning);
        }
        
        EditorGUILayout.EndVertical();
    }
}
```

### 3.2 配置扩展管理器

```csharp
/// <summary>
/// 配置系统扩展管理器
/// </summary>
public static class ConfigExtensionManager
{
    private static bool isInitialized = false;

    /// <summary>
    /// 初始化所有扩展
    /// </summary>
    [RuntimeInitializeOnLoadMethod(RuntimeInitializeLoadType.BeforeSceneLoad)]
    public static void InitializeExtensions()
    {
        if (isInitialized) return;

        // 注册自定义API动作
        RegisterCustomAPIActions();

        // 注册自定义触发器
        RegisterCustomTriggers();

        isInitialized = true;
        GameLogManager.Log("配置系统扩展初始化完成", "ConfigExtensionManager");
    }

    private static void RegisterCustomAPIActions()
    {
        // 注册项目特定的API动作
        ActionConfigParser.RegisterActionType("TeleportUnit", typeof(TeleportUnitAPIAction));
        ActionConfigParser.RegisterActionType("SpawnUnit", typeof(SpawnUnitAPIAction));
        ActionConfigParser.RegisterActionType("ModifySkillCooldown", typeof(ModifySkillCooldownAPIAction));
        ActionConfigParser.RegisterActionType("CreateProjectile", typeof(CreateProjectileAPIAction));
    }

    private static void RegisterCustomTriggers()
    {
        // 注册项目特定的触发器
        TriggerConfigParser.RegisterTriggerType("DistanceTrigger", typeof(DistanceTriggerConfig));
        TriggerConfigParser.RegisterTriggerType("TimeTrigger", typeof(TimeTriggerConfig));
        TriggerConfigParser.RegisterTriggerType("ComboTrigger", typeof(ComboTriggerConfig));
    }

    /// <summary>
    /// 验证所有扩展是否正确注册
    /// </summary>
    public static bool ValidateExtensions()
    {
        bool isValid = true;

        // 验证API动作注册
        var requiredActions = new[] { "TeleportUnit", "SpawnUnit", "ModifySkillCooldown" };
        foreach (var action in requiredActions)
        {
            if (!IsAPIActionRegistered(action))
            {
                GameLogManager.Log($"API动作未注册: {action}", "ConfigExtensionManager", GameLogManager.LogType.Error);
                isValid = false;
            }
        }

        // 验证触发器注册
        var requiredTriggers = new[] { "DistanceTrigger", "TimeTrigger" };
        foreach (var trigger in requiredTriggers)
        {
            if (!IsTriggerRegistered(trigger))
            {
                GameLogManager.Log($"触发器未注册: {trigger}", "ConfigExtensionManager", GameLogManager.LogType.Error);
                isValid = false;
            }
        }

        return isValid;
    }

    private static bool IsAPIActionRegistered(string actionName)
    {
        // 这里需要访问ActionConfigParser的内部注册表
        // 可以通过反射或提供公共API来实现
        return true; // 简化实现
    }

    private static bool IsTriggerRegistered(string triggerName)
    {
        // 这里需要访问TriggerConfigParser的内部注册表
        return true; // 简化实现
    }
}
```

## 4. 最佳实践总结

### 4.1 性能优化建议

1. **合理使用缓存**：
   - 动作配置解析结果应该被缓存
   - 条件评估结果可以短时间缓存
   - 定期清理过期缓存

2. **监控关键指标**：
   - 配置解析耗时
   - 缓存命中率
   - 内存使用情况

3. **优化配置设计**：
   - 避免过于复杂的条件表达式
   - 合理拆分动作配置
   - 使用高效的数据结构

### 4.2 扩展开发指南

1. **API动作开发**：
   - 继承APIActionBase基类
   - 实现参数验证
   - 提供详细的错误信息

2. **触发器开发**：
   - 实现ITriggerConfig接口
   - 考虑性能影响
   - 提供清理机制

3. **测试验证**：
   - 单元测试覆盖新功能
   - 性能测试确保不影响游戏
   - 集成测试验证配置驱动逻辑

通过这些性能优化和扩展功能，GameConfig系统能够在保持高性能的同时，提供强大的配置驱动能力，真正实现企业级的配置管理解决方案。 