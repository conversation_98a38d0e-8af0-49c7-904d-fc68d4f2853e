"use strict";var e=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(exports,"__esModule",{value:!0}),exports.DataModel=void 0;const d=e(require("cli-color")),t=e(require("fs")),i=require("./utils/IOUtils"),T=require("./TSTypeEnum"),n=require("./utils/CommonUtils"),m=require("./CSTypeEnum"),h=require("./utils/StrUtils"),f=require("./SheetType"),p=require("./CodeLang");class C{static get Instance(){return null==this._instance&&(this._instance=new C),this._instance}get config(){var e;return this._config||(e=t.default.readFileSync("Config.json",{encoding:"utf-8"}),this._config=JSON.parse(e)),this._config}get originConfigURL(){return this.config.origin_json_url}get remarkURL(){return this.config.origin_remark_url}get enumURL(){return this.config.origin_enum_url}get originConfig(){var e;return this._originConfig||i.IOUtils.fileOrFolderIsExsit(this.originConfigURL)&&(e=t.default.readFileSync(this.originConfigURL,{encoding:"utf-8"}),this._originConfig=JSON.parse(e)),this._originConfig}get remark(){var e;return this._remark||i.IOUtils.fileOrFolderIsExsit(this.remarkURL)&&(e=t.default.readFileSync(this.remarkURL,{encoding:"utf-8"}),this._remark=JSON.parse(e)),this._remark}get enum(){var e;return this._enum||i.IOUtils.fileOrFolderIsExsit(this.enumURL)&&(e=t.default.readFileSync(this.enumURL,{encoding:"utf-8"}),this._enum=JSON.parse(e)),this._enum}reset(){this._originConfig=null,this._remark=null,this._enum=null}isConventionType(e,t){switch(t){case p.CodeLang.TS:for(var i in T.TSTypeEnum)if(T.TSTypeEnum[i]==e)return!0;break;case p.CodeLang.ETCS:case p.CodeLang.CS:}return!1}getConfigUniqueKeyType(e,t,i=!0){let n;switch(t){case p.CodeLang.TS:n=T.TSTypeEnum;break;case p.CodeLang.ETCS:case p.CodeLang.CS:n=m.CSTypeEnum}var e=this.remark[e];return 1==e.mainKeySubs.length?(e=e.fields[e.mainKeyNames[0]],i&&e.enum?e.enum:e.type?"int"==e.type&&t===p.CodeLang.TS?T.TSTypeEnum.Int:e.type:void 0):n.String}getFixedType(e,t,i){let r;switch(i){case p.CodeLang.TS:r=T.TSTypeEnum;break;case p.CodeLang.ETCS:case p.CodeLang.CS:r=m.CSTypeEnum}i=this.remark[e];let a=((null==i?void 0:i.fields)&&i.fields[t]).type;if(a){a=n.CommonUtils.deepClone(a);var s,e=Array.isArray(a);for(let n=(a=e?a:[a]).length-1;0<=n;n--){let e=a[n],t=e.trim(),i=0;for(;-1!=t.indexOf("[]");)t=t.substring(0,t.length-2),i++;i?(s=h.StrUtils.convertToUpperCamelCase(t),a[n]=r[s+"List"+(1<i?i:"")]):(s=h.StrUtils.convertToUpperCamelCase(e),a[n]=r[""+s])}e||(a=a[0])}return a}getAnyDataValue(e,t){let i="",n=this.originConfig[e];var r=n.fixed_keys.indexOf(t);for(const s in n.data){var a=n.data[s];if(""===i){a=a[r];if(""!==a){i=a;break}}}return i}getConfigKeyType(e,n,r){let a=this.getFixedType(e,n,r);if(a){if(!Array.isArray(a))return a;{let t=!0,i;for(let e=a.length-1;0<=e&&t;e--){i=a[e];for(let e=a.length-1;0<=e&&t;e--){var s=a[e];i!=s&&(t=!1)}}switch(r){case p.CodeLang.TS:return t?i+"[]":"any[]";case p.CodeLang.ETCS:case p.CodeLang.CS:if(t){let e;for(var o in m.CSTypeEnum)m.CSTypeEnum[o]==i&&(e=o);return i==m.CSTypeEnum.Bool||i==m.CSTypeEnum.Int||i==m.CSTypeEnum.Float||i==m.CSTypeEnum.String?m.CSTypeEnum[e+"List"]:(l=e.split("List"),m.CSTypeEnum[l[0]+"List"+(l[1]+1)])}var l=this.remark[e];console.log(d.default.red(`C#不支持Any类型！你清醒一点！表名：${e}，字段：${n}，文件路径：`+(null==l?void 0:l.filePath)))}}}return a=this.getAnyDataValue(e,n),a=this.getValueType(a,r,!1,e,n)}getValueType(r,a,s,o,l){if(s)try{r=JSON.parse(r)}catch(e){s=this.remark[o];console.log(d.default.red(`获取值类型，解析JSON出错！value = ${r}，表名：${o}，字段：${l}，文件路径：`+(null==s?void 0:s.filePath)))}let u;switch(a){case p.CodeLang.TS:u=T.TSTypeEnum;break;case p.CodeLang.ETCS:case p.CodeLang.CS:u=m.CSTypeEnum}if(""===r)switch(a){case p.CodeLang.TS:return T.TSTypeEnum.Any;case p.CodeLang.ETCS:case p.CodeLang.CS:return m.CSTypeEnum.String}if(!0===r||!1===r)return u.Bool;if("number"==typeof r)switch(a){case p.CodeLang.TS:return T.TSTypeEnum.Int;case p.CodeLang.ETCS:case p.CodeLang.CS:return n.CommonUtils.numIsInt(r)?m.CSTypeEnum.Int:m.CSTypeEnum.Float}if("string"==typeof r)return u.String;if(Array.isArray(r)){if(0==r.length)switch(a){case p.CodeLang.TS:return T.TSTypeEnum.AnyList;case p.CodeLang.ETCS:case p.CodeLang.CS:var g=this.remark[o];console.log(d.default.red(`C#不支持解析空数组类型！表名：${o}，字段：${l}，文件路径：`+(null==g?void 0:g.filePath)))}var C,y,f=this.getArrayInfo(r,a);let t,e=Object.keys(f);switch(a){case p.CodeLang.TS:t=Object.keys(f).length<=1;break;case p.CodeLang.ETCS:case p.CodeLang.CS:t=2==e.length&&-1!=e.indexOf(m.CSTypeEnum.Int)&&-1!=e.indexOf(m.CSTypeEnum.Float)||e.length<=1}let i=0;if(t){let e=[];for(var c in f)for(C in f[c])+C>i&&(i=+C),-1==e.indexOf(+C)&&e.push(+C);1<e.length&&(t=!1)}let n;if(t)switch(a){case p.CodeLang.TS:n=e[0];break;case p.CodeLang.ETCS:case p.CodeLang.CS:n=2==e.length&&-1!=e.indexOf(m.CSTypeEnum.Int)&&-1!=e.indexOf(m.CSTypeEnum.Float)?m.CSTypeEnum.Float:e[0]}if(t)return s=1<i?i:"",y=h.StrUtils.convertToUpperCamelCase(n),u[y+"List"+s];switch(a){case p.CodeLang.TS:return T.TSTypeEnum.AnyList;case p.CodeLang.ETCS:case p.CodeLang.CS:var S=this.remark[o];console.log(d.default.red(`C#不支持Any类型的数组！表名：${o}，字段：${l}，文件路径：`+(null==S?void 0:S.filePath)))}}if(r instanceof Object)switch(a){case p.CodeLang.TS:return T.TSTypeEnum.Any;case p.CodeLang.ETCS:case p.CodeLang.CS:var e=this.remark[o];console.log(d.default.red(`C#不支持Any类型！表名：${o}，字段：${l}，文件路径：`+(null==e?void 0:e.filePath)))}}getArrayInfo(e,i,n,r){return null==n&&(n={}),null==r&&(r=1),Array.isArray(e)&&e.forEach((e,t)=>{Array.isArray(e)?this.getArrayInfo(e,i,n,r+1):(e=this.getValueType(e,i))&&(n[e]||(n[e]={}),n[e][r]||(n[e][r]=0),n[e][r]++)}),n}getParents(e,t){let i,n=this.remark[e];for(;n;){var r=n.parent;n=r?(i?i.push(r):i=[r],this.remark[r]):null}return i&&t&&i.unshift(e),i}getConfigLinks(e){let t;var i=this.remark[e];if(null!=i&&i.fields)for(var n in i.fields){var n=i.fields[n];!n||(n=n.link)&&(t?t.push(n):t=[n])}return t}getMainKeyVarName(e){return C.MainKeyVarName+e}isMainKey(e,t){if(t.substring(0,C.MainKeyVarName.length)==C.MainKeyVarName)return!0;let i=this.remark[e];return!(null===i||void 0===i||!i.mainKeyNames)&&-1!=i.mainKeyNames.indexOf(t)}getConfigNamesAndCutDataByConfigType(n){var r;let u=Object.keys(this.originConfig);for(let e=u.length-1;0<=e;e--){var a=u[e];let i=this.originConfig[a];var s=this.remark[a];let t=!1;if(0==(null!=s&&s.fields?Object.keys(s).length:0))t=!0;else{var o=s.sheetType;let e;o==f.SheetType.Horizontal?e=i.fixed_keys:o==f.SheetType.Vertical&&(e=Object.keys(i));for(let t=e.length-1;0<=t;t--){var l=e[t];if(!(0<=(null==(r=s.fields[l].generate)?void 0:r.indexOf(n))))if(e.splice(t,1),o==f.SheetType.Horizontal)for(var g in i.data){let e=i.data[g];e.splice(t,1)}else delete i[l]}0==e.length&&(t=!0)}t&&u.splice(e,1)}for(;;){let s,o,l;for(let a=u.length-1;!s&&0<=a;a--)for(let r=u.length-1;!s&&0<=r;r--)if(a!=r){var C=u[a],y=u[r];let e=this.getConfigLinks(C),t=this.getConfigLinks(y),i=(e&&-1!=e.indexOf(y)?a<r&&(s=!0,o=a,l=r):t&&-1!=t.indexOf(C)&&r<a&&(s=!0,o=a,l=r),this.getParents(C)),n=this.getParents(y);i&&-1!=i.indexOf(y)?a<r&&(s=!0,o=a,l=r):n&&-1!=n.indexOf(C)&&r<a&&(s=!0,o=a,l=r)}if(!s)break;var e=u[l];u[l]=u[o],u[o]=e}return u}getConfigFixedKeys(e,r,t){let i=[],a=(t&&((i=this.getParents(e))?i.reverse():i=[]),i.push(e),[]);return i.forEach(e=>{var t=this.originConfig[e],i=this.remark[e].sheetType;let n;i==f.SheetType.Horizontal?n=t.fixed_keys:i==f.SheetType.Vertical&&(n=Object.keys(t)),n.forEach(t=>{a.find(e=>e[0]==t)||a.push([t,this.getConfigKeyType(e,t,r),e])})}),a}getConfigCollectionTypeByIndex(t,i,n,r){var a=this.remark[t].mainKeyNames,s=t+C.Instance.config.export_item_suffix;n=n||0,r=r||a.length-1;let o;switch(i){case p.CodeLang.TS:o="Map";break;case p.CodeLang.ETCS:case p.CodeLang.CS:o="Dictionary"}let l="",u="";for(let e=n;e<=r;e++){var g=a[e],g=C.Instance.getConfigKeyType(t,g,i);l+=o+`<${g}, `,e==r&&(l+=""+s),u+=">"}return l+=u}getParseFuncNameByType(e,t){switch(t){case p.CodeLang.TS:console.log(d.default.red("TS没有这个烦恼，是从哪里调进来的？！你清醒一点！"));break;case p.CodeLang.ETCS:case p.CodeLang.CS:switch(e){case m.CSTypeEnum.Bool:return"ConfigUtility.ParseBool({0})";case m.CSTypeEnum.Int:return"ConfigUtility.ParseInt({0})";case m.CSTypeEnum.Float:return"ConfigUtility.ParseFloat({0})";case m.CSTypeEnum.String:return"{0}";case m.CSTypeEnum.BoolList:return"ConfigUtility.ParseBoolList({0})";case m.CSTypeEnum.IntList:return"ConfigUtility.ParseIntList({0})";case m.CSTypeEnum.FloatList:return"ConfigUtility.ParseFloatList({0})";case m.CSTypeEnum.StringList:return"ConfigUtility.ParseStringList({0})";case m.CSTypeEnum.BoolList2:return"ConfigUtility.ParseBoolList2({0})";case m.CSTypeEnum.IntList2:return"ConfigUtility.ParseIntList2({0})";case m.CSTypeEnum.FloatList2:return"ConfigUtility.ParseFloatList2({0})";case m.CSTypeEnum.StringList2:return"ConfigUtility.ParseStringList2({0})";case m.CSTypeEnum.BoolList3:return"ConfigUtility.ParseBoolList3({0})";case m.CSTypeEnum.IntList3:return"ConfigUtility.ParseIntList3({0})";case m.CSTypeEnum.FloatList3:return"ConfigUtility.ParseFloatList3({0})";case m.CSTypeEnum.StringList3:return"ConfigUtility.ParseStringList3({0})";case m.CSTypeEnum.BoolList4:return"ConfigUtility.ParseBoolList4({0})";case m.CSTypeEnum.IntList4:return"ConfigUtility.ParseIntList4({0})";case m.CSTypeEnum.FloatList4:return"ConfigUtility.ParseFloatList4({0})";case m.CSTypeEnum.StringList4:return"ConfigUtility.ParseStringList4({0})";case m.CSTypeEnum.BoolList5:return"ConfigUtility.ParseBoolList5({0})";case m.CSTypeEnum.IntList5:return"ConfigUtility.ParseIntList5({0})";case m.CSTypeEnum.FloatList5:return"ConfigUtility.ParseFloatList5({0})";case m.CSTypeEnum.StringList5:return"ConfigUtility.ParseStringList5({0})"}}}valEquip(e,t){var i=Array.isArray(e),n=Array.isArray(t);return i&&n?e==t||JSON.stringify(e)==JSON.stringify(t):!i&&!n&&e==t}}(exports.DataModel=C).MainKeyVarName="mainKey";