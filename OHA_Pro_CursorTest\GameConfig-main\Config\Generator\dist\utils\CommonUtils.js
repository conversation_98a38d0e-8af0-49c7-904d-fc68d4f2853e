"use strict";var t=this&&this.__importDefault||function(t){return t&&t.__esModule?t:{default:t}};Object.defineProperty(exports,"__esModule",{value:!0}),exports.CommonUtils=void 0;const r=t(require("fs")),n=require("./IOUtils"),s=t(require("cli-color")),a=require("./StrUtils"),i=require("../CodeLang");class e{static getTemplate(t,e){t=`templates/${t.template_name||t.id}/template/`+e;if(n.IOUtils.fileOrFolderIsExsit(t))return r.default.readFileSync(t,"utf-8");console.log(s.default.red("找不到模板文件！"+t))}static numIsInt(t){return""!==t&&!isNaN(t)&&parseInt(t)==parseFloat(t)}static numIsFloat(t){return""!==t&&!isNaN(t)&&parseInt(t)!=parseFloat(t)}static deepClone(r){if(r instanceof Array){let e=[];var n=r.length;for(let t=0;t<n;t++)e.push(this.deepClone(r[t]));return e}if(r instanceof Object){let t={};for(var e in r)r.hasOwnProperty(e)&&(t[e]=this.deepClone(r[e]));return t}return r}static convertStringToObj(t,e){let r={},n,s=!1;e&&t&&t.replace&&(t=t.replace(/\t/g,"").replace(/\r/g,"").replace(/\n/g,""));try{n=JSON.parse(t),s=!0}catch(t){}return r.obj=s?n:t,r.isString="string"==typeof r.obj,r}static getCommentStr(t,e,r=0,n){if(null==e)return"";"string"!=typeof e&&(e+=""),e.replace(/\r/,"");var s=e.split("\n");let l="";switch(t){case i.CodeLang.CS:case i.CodeLang.ETCS:l+=a.StrUtils.getIndentStr(r)+"/// <summary>\n";for(let t=0;t<s.length;t++)l+=a.StrUtils.getIndentStr(r)+"/// "+s[t]+"\n";l+=a.StrUtils.getIndentStr(r,n)+"/// </summary>";break;case i.CodeLang.TS:l+=a.StrUtils.getIndentStr(r)+"/**\n";for(let t=0;t<s.length;t++)l+=a.StrUtils.getIndentStr(r)+" * "+s[t]+"\n";l+=a.StrUtils.getIndentStr(r,n)+" */"}return l}}exports.CommonUtils=e;