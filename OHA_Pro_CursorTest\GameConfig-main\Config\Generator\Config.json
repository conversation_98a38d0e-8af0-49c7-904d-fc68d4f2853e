{"excel_url": "E:/Unity Project/OHA_CursorProject_git/OHA_Pro_CursorTest/Assets/StreamingAssets/GameConfig/", "excel_override_enabled": true, "excel_override_warning_enabled": true, "excel_extend_max_layer": 100, "export_suffix": "Config", "export_item_suffix": "<PERSON><PERSON>", "export_collection_suffix": "Map", "export_data_splitor_random_enabled": false, "export_data_splitor": "_D3A_", "origin_export_url": "../.cache/", "origin_json_url": "../.cache/config.min.json", "origin_remark_url": "../.cache/.remarks.json", "origin_enum_url": "../.cache/.enums.json", "origin_extends_url": "../.cache/.extends.json", "incrementalPublish": true, "exports": [{"id": 1, "enabled": true, "code_language": "cs", "script_suffix": "cs", "template_name": "", "force_make_dir": true, "export_url": "E:/Unity Project/OHA_CursorProject_git/OHA_Pro_CursorTest/Assets/Resources/Config/Config.txt", "export_script_url": "../../../Assets/Scripts/GameConfig/Runtime/", "export_config_manager_name": "ConfigMgr"}, {"id": 2, "enabled": false, "code_language": "ts", "script_suffix": "ts", "template_name": "", "force_make_dir": true, "export_url": "../../Example/CCCProject/assets/resources/config/config.json", "export_script_url": "../../Example/CCCProject/assets/scripts/config/", "export_config_manager_name": "ConfigMgr"}]}