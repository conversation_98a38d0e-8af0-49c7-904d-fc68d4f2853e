using System.Collections.Generic;
using System.Text.RegularExpressions;
using UnityEngine;

namespace GameConfig
{
    public static class ConfigMgr
    {
        public static KVConfig KVConfig { private set; get; }

        public static void Init(string configPath)
        {
            var textAsset = Resources.Load(configPath) as TextAsset;
            var configText = ConfigUtility.DecodeBase64(textAsset.text);
            Parse(configText);
        }
        
        public static void Parse(string configText)
        {
            var sections = configText.Split("#"[0]);

            string section;
            string[] lines;

            // KVConfig
            section = sections[0];
            lines = Regex.Split(section, "\r\n");
            KVConfig = new KVConfig("KVConfig", lines[0], lines[1], ConfigUtility.ParseIntList(lines[2]), ConfigUtility.ParseIntList2(lines[3]), ConfigUtility.ParseStringList(lines[4]), ConfigUtility.ParseInt(lines[5]), ConfigUtility.ParseBool(lines[6]));
        }
    }
}