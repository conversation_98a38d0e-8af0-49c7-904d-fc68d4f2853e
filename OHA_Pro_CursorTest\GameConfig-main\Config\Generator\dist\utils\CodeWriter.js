"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.CodeWriter=void 0;const t=require("./LineBreak"),n=require("./StrUtils");class e{get lineBreak(){return this._lineBreak}set lineBreak(e){switch(this._lineBreak=e){case t.LineBreak.CRLF:this._lineBreakStr="\r\n";break;case t.LineBreak.LF:this._lineBreakStr="\n"}}get lineBreakStr(){return this._lineBreakStr}get content(){return this._content}get addCount(){return this._addCount}constructor(){this._content="",this._addCount=0,this.lineBreak=t.LineBreak.CRLF}add(e,t,r=1){this._content+=n.StrUtils.getIndentStr(e)+t,"number"==typeof r?0<r&&this.newLine(r):!0===r&&this.newLine(1),this._addCount++}addStr(e){this._content+=e}newLine(e=1){for(;0<e;)this._content+=this._lineBreakStr,e--}clear(){this._content="",this._addCount=0}}exports.CodeWriter=e;