"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.StringUtils=void 0;class e{static format(e,...t){if(null!=e&&0<arguments.length){if(1!=arguments.length)if(2==arguments.length&&"object"==typeof t){if(!t)return"";for(var r=0;r<t.length;r++)null!=t[r]&&(n=new RegExp("([【{]"+r+"[】}])","g"),e=e.replace(n,t[r].toString()))}else for(var n,r=1;r<arguments.length;r++)null!=arguments[r]&&(n=new RegExp("([【{]"+(r-1)+"[】}])","g"),e=e.replace(n,arguments[r].toString()));return e}return""}static getStrNum(e){let t=[],r=e.match(/\d+/g);if(r)return r.forEach(e=>{t.push(+e)}),1==t.length?t[0]:t}static convertToLowerCamelCase(e,t=!1){return(t?"_":"")+(e=this.convertToNoUnderline(e))[0].toLowerCase()+e.substring(1,e.length)}static convertToUpperCamelCase(e){return(e=this.convertToNoUnderline(e))[0].toUpperCase()+e.substring(1,e.length)}static convertToNoUnderline(e){var t=e;if(0<=e.indexOf("_")){var t="",r=e.split("_");for(let e=0;e<r.length;e++){var n=r[e];0<e?t+=n[0].toUpperCase()+n.substring(1,n.length):t+=n}}return t}static genPassword(e=8,t=!0,r=!0,n=!1,o=!0,s=!1){var a="";if(t||r||n){for(var l=e;0<=l;l--){var i=Math.floor(94*Math.random()+33);!t&&48<=i&&i<=57||!r&&(65<=i&&i<=90||97<=i&&i<=122)||!n&&(33<=i&&i<=47||58<=i&&i<=64||91<=i&&i<=96||123<=i&&i<=127)?l++:a+=String.fromCharCode(i)}null==o||o||(a=s?a.toLowerCase():a.toUpperCase())}return a}}exports.StringUtils=e;