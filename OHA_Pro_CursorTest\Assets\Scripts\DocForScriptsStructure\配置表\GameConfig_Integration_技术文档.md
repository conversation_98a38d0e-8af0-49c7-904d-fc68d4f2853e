# GameConfig系统集成技术文档

## 概述

将GameConfig配置工具集成到Unity动作游戏项目，实现数据驱动开发。策划通过Excel配置游戏数据和逻辑，程序专注框架开发。

### 核心目标
- **数据外部化**：配置数据迁移到Excel
- **逻辑配置**：支持复杂游戏逻辑配置
- **系统集成**：与技能、Buff、单位系统无缝集成

## 1. 核心配置字段

### 1.1 主要配置表
- **SkillConfig** - 技能基础配置（ID、名称、帧数、能量消耗等）
- **SkillLogicConfig** - 技能逻辑配置（触发帧、条件、动作）
- **BuffConfig** - Buff基础配置（ID、名称、持续时间等）
- **BuffLogicConfig** - Buff逻辑配置（逻辑类型、条件、动作）
- **UnitConfig** - 单位配置（属性、技能列表、AI类型）

### 1.2 关键设计原则
- **攻击盒数据通过代码创建**：不在配置表中配置hitBoxData
- **使用energyCost而非manaCost**：与游戏能量系统保持一致
- **特效通过逻辑配置处理**：不使用effectPath字段

## 2. GameConfig工作原理

### 2.1 数据流程
**Excel配置表 → GameConfig生成器 → C#数据模型类 + 数据文件 → ConfigMgr管理类 → 游戏运行时**

### 2.2 核心特性
- **表格格式**：横表、纵表、枚举表、继承表
- **数据类型**：基础类型、数组、枚举、表连接
- **生成产物**：数据模型类、管理器类、枚举定义、数据文件

## 3. 项目集成架构

### 3.1 三层架构
- **配置层**：Excel配置表 → GameConfig生成器 → 生成的配置代码
- **适配层**：SkillConfigAdapter、BuffConfigAdapter、UnitConfigAdapter
- **系统层**：SkillSystem、BuffSystem、UnitSystem

### 3.2 设计规范
- **命名规范**：表名PascalCase，字段名camelCase，枚举PascalCase
- **目录结构**：按系统分类组织配置表

## 4. 配置表设计

### 4.1 技能配置表
- **SkillConfig** - 基础配置（ID、名称、类型、帧数、能量消耗、动画等）
- **SkillLogicConfig** - 逻辑配置（触发帧、条件、动作配置）

### 4.2 动作配置格式
**格式**：`动作类型:参数名=参数值,参数名=参数值;动作类型:参数名=参数值`

**支持的动作**：
- `AddBuff:buffId=buff_fire,target=self`
- `RemoveBuff:buffId=buff_shield,target=target`
- `AttributeModify:attribute=health,value=-100,target=target`
- `PlayFX:fxName=explosion,duration=2.0,target=hitTarget`
- `DealDamage:damage=150,damageType=Magic,target=hitTarget`

### 4.3 技能配置适配器
**核心方法**：
- `LoadSkillFromConfig()` - 加载技能配置
- `ApplyConfigInternal()` - 应用配置到技能实例
- `LoadSkillLogicConfig()` - 加载逻辑配置并注册到指定帧
- `ExecuteLogicConfig()` - 执行逻辑配置（条件评估+动作执行）

## 5. Buff系统配置

### 5.1 Buff配置表
- **BuffConfig** - 基础配置（ID、名称、持续时间、重复添加方式等）
- **BuffLogicConfig** - 逻辑配置（逻辑类型、动作配置、触发器配置、执行条件）

### 5.2 Buff逻辑类型
- **OnStart** - Buff开始时执行
- **OnEnd** - Buff结束时执行
- **OnTick** - 周期性执行
- **OnLayerChange** - 层数变化时执行
- **OnRemove** - Buff被移除时执行

### 5.3 Buff配置适配器
**核心方法**：
- `CreateBuffFromConfig()` - 创建Buff配置
- `ApplyConfigInternal()` - 添加Buff并应用配置
- `LoadBuffLogicConfig()` - 加载Buff逻辑配置
- `RegisterBuffLogic()` - 根据逻辑类型注册Buff逻辑
- `ExecuteBuffLogic()` - 执行Buff逻辑（条件评估+动作执行）



## 6. 单位系统配置

### 6.1 单位配置表
- **CharacterConfig** - 角色配置（属性、技能列表、初始Buff）
- **MonsterConfig** - 怪物配置（属性、技能列表、AI类型、掉落物品）

### 6.2 单位配置适配器
**核心方法**：
- `ApplyCharacterConfig()` - 应用角色配置到Unit
- `ApplyMonsterConfig()` - 应用怪物配置到Unit
- `ApplyBaseAttributes()` - 应用基础属性
- `LoadUnitSkills()` - 加载单位技能列表
- `ApplyInitialBuffs()` - 应用初始Buff列表

## 7. 配置适配器架构

### 7.1 适配器接口设计
**IConfigAdapter<TConfig, TTarget>接口**：
- `ApplyConfig()` - 应用配置到目标对象
- `ValidateConfig()` - 验证配置数据有效性

**ConfigAdapterBase<TConfig, TTarget>抽象基类**：
- 提供标准的配置应用流程
- 统一的错误处理和日志记录

## 8. 逻辑配置系统

### 8.1 设计目标
实现配置与代码的彻底分离，策划通过Excel配置复杂游戏逻辑。

### 8.2 条件配置格式
- **API调用比较**：`GetHealth_API(target) < 0.5`
- **逻辑运算符**：`&&`、`||`、`!`
- **括号分组**：`(condition1 && condition2) || !condition3`

### 8.3 核心组件
**ConditionEvaluator**：
- 专注于基本数值比较操作
- 通过API调用获取具体数值
- 支持复杂逻辑表达式

**LogicExecutionContext**：
- 包含条件评估所需的上下文信息









### 8.4 设计决策：不支持if/else/while

**原因**：
- 现有条件+动作机制已足够强大
- 保持配置简洁性，降低策划使用门槛
- 避免性能开销和安全风险

**替代方案**：
- 通过多个配置项组合实现复杂逻辑
- 使用条件字段实现分支逻辑
- 通过不同帧数执行不同逻辑配置

## 9. 现有代码集成

### 9.1 集成方式
**采用扩展方法而非继承**：
- 通过扩展方法为现有类添加配置驱动功能
- 保持现有类结构完整性
- 非侵入式集成，便于在现有项目中平滑集成

### 9.2 技能系统集成
**SkillBase扩展**：
- 添加配置驱动逻辑支持
- 在指定帧注册和执行逻辑配置

### 9.3 Buff系统集成
**BuffHandler扩展**：
- 添加配置驱动的Buff创建功能
- 支持批量应用初始Buff配置

**Buff类扩展**：
- 根据逻辑类型注册配置逻辑
- 支持各种Buff生命周期事件

### 9.4 单位系统集成
**Unit类扩展**：
- 支持角色和怪物配置应用
- 运行时配置切换功能

### 9.5 CrossSystemAPI集成
**API扩展**：
- 为各系统添加配置驱动的API执行功能
- 统一的条件评估和动作执行接口

### 9.6 配置热更新
**热更新管理器**：
- 支持运行时配置重载
- 通知所有配置驱动对象更新

## 10. 实施步骤

### 10.1 环境准备
1. **安装GameConfig工具** - 下载、安装依赖、配置生成器
2. **项目目录结构** - 创建配置代码、适配器、解析器目录

### 10.2 配置表创建
**主要配置表**：SkillConfig、BuffConfig、UnitConfig及对应的逻辑配置表
**枚举表**：各系统相关的枚举定义

### 10.3 代码生成与集成
1. **执行生成** - 运行生成器，验证生成的配置类
2. **实现解析器** - 创建动作配置解析器、条件评估器等
3. **实现适配器** - 为各系统实现配置适配器
4. **系统集成** - 通过扩展方法集成到现有系统

## 11. 最佳实践

### 11.1 设计原则
- **数据规范化**：单一职责、避免冗余、类型安全、逻辑分离
- **命名规范**：表名PascalCase、字段名camelCase、有意义的ID
- **版本控制**：Excel文件和生成代码纳入版本控制

### 11.2 逻辑配置
- **条件表达式**：简洁明了、避免复杂嵌套、提供错误处理
- **动作配置**：模块化设计、参数验证、明确执行顺序

### 11.3 性能优化
- **数据加载**：配置缓存、按需加载
- **逻辑配置**：动作配置缓存、触发器配置缓存
- **内存管理**：合理缓存策略、及时资源释放

### 11.4 错误处理
- **配置验证**：验证配置有效性
- **降级处理**：提供默认配置、安全执行机制

## 12. 测试与验证

### 12.1 测试类型
- **单元测试**：配置加载、适配器功能、逻辑配置解析
- **集成测试**：完整配置流程、系统间集成
- **性能测试**：配置加载性能、逻辑配置解析性能

## 13. 故障排除

### 13.1 常见问题
- **配置加载失败**：检查文件路径、验证配置格式
- **生成代码编译错误**：检查Excel表格格式、字段类型定义
- **配置数据不匹配**：重新生成配置文件、确认数据保存
- **逻辑配置解析失败**：验证配置字符串格式、参数有效性
- **条件表达式评估错误**：检查语法、确认变量存在

### 13.2 调试工具
**ConfigDebugger**：
- 提供Unity菜单项验证所有配置
- 测试逻辑配置解析功能
- 生成详细的验证报告

## 14. 总结

### 14.1 集成收益
- **开发效率提升**：策划独立配置，缩短迭代周期
- **系统可维护性增强**：配置与代码分离，强类型安全
- **团队协作优化**：明确职责分工，标准化流程
- **系统扩展性增强**：快速实现新功能，灵活的配置机制

### 14.2 技术架构优势
- **彻底的配置与代码分离**：数值和逻辑配置都可通过Excel管理
- **强类型安全**：自动生成的C#配置类，编译期错误检查
- **高性能设计**：配置解析缓存，优化的条件评估算法
- **易于维护**：统一的适配器接口，标准化的错误处理
- **良好的扩展性**：模块化架构，非侵入式功能扩展

### 14.3 实施建议
1. **分阶段实施**：基础适配器 → 系统集成 → 性能优化
2. **团队协作**：程序负责框架，策划负责配置
3. **测试验证**：单元测试、集成测试、性能测试
4. **文档维护**：保持技术文档和使用指南的及时更新

通过本技术文档的指导，团队可以成功地将GameConfig系统集成到现有项目中，实现数据驱动的游戏开发模式，真正实现配置与代码的彻底分离，提升开发效率和产品质量。
            var critRateAttr = caster.GetAttribute("CritRate_Attribute");
            var critDamageAttr = caster.GetAttribute("CritDamageMultiplier_Attribute");
            
            if (critRateAttr != null && critDamageAttr != null)
            {
                float critChance = critRateAttr.Value / 100f;
                if (UnityEngine.Random.value < critChance)
                {
                    finalDamage *= critDamageAttr.Value;
                    GameLogManager.Log("暴击！", "DealDamageAPIAction");
                }
            }
        }

        return finalDamage;
    }

    public override bool ValidateParameters()
    {
        return base.ValidateParameters() && damage > 0;
    }
}
```

#### 14.3.4 PlayFXAPIAction

```csharp
/// <summary>
/// 播放特效的API动作
/// 配置格式：PlayFX:fxName=explosion,duration=2.0,target=hitTarget,offset=0,1,0
/// </summary>
public class PlayFXAPIAction : APIActionBase
{
    private string fxName;
    private float duration;
    private string targetType;
    private Vector3 offset;

    public override void Initialize(Dictionary<string, string> parameters)
    {
        base.Initialize(parameters);
        
        fxName = GetStringParameter("fxName");
        duration = GetFloatParameter("duration", 1.0f);
        targetType = GetStringParameter("target", "target");
        
        // 解析偏移量
        string offsetStr = GetStringParameter("offset", "0,0,0");
        offset = ParseVector3(offsetStr);
    }

    public override void Execute(APIExecutionContext context)
    {
        if (string.IsNullOrEmpty(fxName))
        {
            GameLogManager.Log("PlayFX动作缺少fxName参数", "PlayFXAPIAction", GameLogManager.LogType.Warning);
            return;
        }

        var targetGO = GetTargetGameObject(targetType, context);
        Vector3 position = context.Position;
        
        if (targetGO != null)
        {
            position = targetGO.transform.position + offset;
        }

        // 加载并播放特效
        var fxPrefab = Resources.Load<GameObject>($"FX/{fxName}");
        if (fxPrefab != null)
        {
            var fxInstance = Object.Instantiate(fxPrefab, position, Quaternion.identity);
            
            // 设置特效持续时间
            if (duration > 0)
            {
                Object.Destroy(fxInstance, duration);
            }

            GameLogManager.Log($"播放特效: {fxName} 在位置 {position}", "PlayFXAPIAction");
        }
        else
        {
            GameLogManager.Log($"找不到特效预制体: FX/{fxName}", "PlayFXAPIAction", GameLogManager.LogType.Warning);
        }
    }

    private Vector3 ParseVector3(string vectorStr)
    {
        var parts = vectorStr.Split(',');
        if (parts.Length == 3)
        {
            if (float.TryParse(parts[0], out float x) &&
                float.TryParse(parts[1], out float y) &&
                float.TryParse(parts[2], out float z))
            {
                return new Vector3(x, y, z);
            }
        }
        return Vector3.zero;
    }

    public override bool ValidateParameters()
    {
        return base.ValidateParameters() && !string.IsNullOrEmpty(fxName);
    }
}
```

### 14.4 触发器配置解析器

```csharp
/// <summary>
/// 触发器配置解析器
/// </summary>
public static class TriggerConfigParser
{
    private static readonly Dictionary<string, Type> TriggerTypeMap = new Dictionary<string, Type>
    {
        { "DamageTrigger", typeof(DamageTriggerConfig) },
        { "HealthTrigger", typeof(HealthTriggerConfig) },
        { "BuffTrigger", typeof(BuffTriggerConfig) },
        { "SkillTrigger", typeof(SkillTriggerConfig) },
        { "TimeTrigger", typeof(TimeTriggerConfig) },
        { "DistanceTrigger", typeof(DistanceTriggerConfig) }
    };

    /// <summary>
    /// 解析触发器配置字符串
    /// </summary>
    public static ITriggerConfig ParseTriggerConfig(string triggerConfig)
    {
        if (string.IsNullOrEmpty(triggerConfig))
            return null;

        try
        {
            // 解析格式：TriggerType:param1=value1,param2=value2
            var colonIndex = triggerConfig.IndexOf(':');
            if (colonIndex <= 0)
            {
                GameLogManager.Log($"触发器配置格式错误: {triggerConfig}", 
                                 "TriggerConfigParser", GameLogManager.LogType.Warning);
                return null;
            }

            var triggerType = triggerConfig.Substring(0, colonIndex).Trim();
            var parametersString = triggerConfig.Substring(colonIndex + 1).Trim();

            // 获取触发器类型
            if (!TriggerTypeMap.TryGetValue(triggerType, out Type triggerClass))
            {
                GameLogManager.Log($"未知的触发器类型: {triggerType}", 
                                 "TriggerConfigParser", GameLogManager.LogType.Warning);
                return null;
            }

            // 解析参数
            var parameters = ParseParameters(parametersString);

            // 创建触发器配置实例
            return CreateTriggerConfigInstance(triggerClass, parameters);
        }
        catch (Exception e)
        {
            GameLogManager.Log($"解析触发器配置失败: {triggerConfig}, 错误: {e.Message}", 
                             "TriggerConfigParser", GameLogManager.LogType.Error);
            return null;
        }
    }

    private static Dictionary<string, string> ParseParameters(string parametersString)
    {
        var parameters = new Dictionary<string, string>();

        if (string.IsNullOrEmpty(parametersString))
            return parameters;

        var paramPairs = parametersString.Split(',');
        foreach (var paramPair in paramPairs)
        {
            var equalIndex = paramPair.IndexOf('=');
            if (equalIndex > 0)
            {
                var key = paramPair.Substring(0, equalIndex).Trim();
                var value = paramPair.Substring(equalIndex + 1).Trim();
                parameters[key] = value;
            }
        }

        return parameters;
    }

    private static ITriggerConfig CreateTriggerConfigInstance(Type triggerType, Dictionary<string, string> parameters)
    {
        try
        {
            var triggerConfig = Activator.CreateInstance(triggerType) as ITriggerConfig;
            if (triggerConfig != null)
            {
                triggerConfig.Initialize(parameters);
            }
            return triggerConfig;
        }
        catch (Exception e)
        {
            GameLogManager.Log($"创建触发器配置实例失败: {triggerType.Name}, 错误: {e.Message}", 
                             "TriggerConfigParser", GameLogManager.LogType.Error);
            return null;
        }
    }

    /// <summary>
    /// 注册新的触发器类型
    /// </summary>
    public static void RegisterTriggerType(string triggerName, Type triggerType)
    {
        if (!typeof(ITriggerConfig).IsAssignableFrom(triggerType))
        {
            GameLogManager.Log($"触发器类型必须实现ITriggerConfig接口: {triggerType.Name}", 
                             "TriggerConfigParser", GameLogManager.LogType.Error);
            return;
        }

        TriggerTypeMap[triggerName] = triggerType;
        GameLogManager.Log($"注册新触发器类型: {triggerName} -> {triggerType.Name}", "TriggerConfigParser");
    }
}

/// <summary>
/// 触发器配置接口
/// </summary>
public interface ITriggerConfig
{
    void Initialize(Dictionary<string, string> parameters);
    void CreateTrigger(Unit targetUnit, object source = null);
    bool ValidateParameters();
}

/// <summary>
/// 伤害触发器配置
/// 配置格式：DamageTrigger:threshold=100,target=enemy,action=AddBuff
/// </summary>
public class DamageTriggerConfig : ITriggerConfig
{
    private float threshold;
    private string targetType;
    private string action;

    public void Initialize(Dictionary<string, string> parameters)
    {
        threshold = GetFloatParameter(parameters, "threshold", 0f);
        targetType = GetStringParameter(parameters, "target", "enemy");
        action = GetStringParameter(parameters, "action", "");
    }

    public void CreateTrigger(Unit targetUnit, object source = null)
    {
        // 创建伤害触发器的具体实现
        // 这里可以使用现有的触发器系统或创建新的触发器组件
        GameLogManager.Log($"创建伤害触发器: 阈值={threshold}, 目标={targetType}", "DamageTriggerConfig");
    }

    public bool ValidateParameters()
    {
        return threshold >= 0;
    }

    private float GetFloatParameter(Dictionary<string, string> parameters, string key, float defaultValue)
    {
        if (parameters.TryGetValue(key, out string value) && float.TryParse(value, out float result))
        {
            return result;
        }
        return defaultValue;
    }

    private string GetStringParameter(Dictionary<string, string> parameters, string key, string defaultValue)
    {
        return parameters.TryGetValue(key, out string value) ? value : defaultValue;
    }
}
```

## 15. 配置验证和错误处理增强

### 15.1 配置完整性验证器

```csharp
/// <summary>
/// 配置完整性验证器，提供全面的配置验证功能
/// </summary>
public static class ConfigIntegrityValidator
{
    /// <summary>
    /// 验证所有配置的完整性
    /// </summary>
    public static ValidationResult ValidateAllConfigs()
    {
        var result = new ValidationResult();

        // 验证技能配置
        ValidateSkillConfigs(result);

        // 验证Buff配置
        ValidateBuffConfigs(result);

        // 验证单位配置
        ValidateUnitConfigs(result);

        // 验证逻辑配置
        ValidateLogicConfigs(result);

        // 验证引用完整性
        ValidateReferences(result);

        return result;
    }

    private static void ValidateSkillConfigs(ValidationResult result)
    {
        foreach (var skill in ConfigMgr.SkillConfig.Data.Values)
        {
            var skillResult = ValidateSkillConfig(skill);
            result.AddResult($"技能配置 {skill.SkillId}", skillResult);
        }
    }

    private static void ValidateBuffConfigs(ValidationResult result)
    {
        foreach (var buff in ConfigMgr.BuffConfig.Data.Values)
        {
            var buffResult = ValidateBuffConfig(buff);
            result.AddResult($"Buff配置 {buff.BuffId}", buffResult);
        }
    }

    private static void ValidateLogicConfigs(ValidationResult result)
    {
        // 验证技能逻辑配置
        foreach (var logicConfig in ConfigMgr.SkillLogicConfig.Data.Values)
        {
            var logicResult = ValidateSkillLogicConfig(logicConfig);
            result.AddResult($"技能逻辑配置 {logicConfig.SkillId}", logicResult);
        }

        // 验证Buff逻辑配置
        foreach (var logicConfig in ConfigMgr.BuffLogicConfig.Data.Values)
        {
            var logicResult = ValidateBuffLogicConfig(logicConfig);
            result.AddResult($"Buff逻辑配置 {logicConfig.BuffId}", logicResult);
        }
    }

    private static void ValidateReferences(ValidationResult result)
    {
        // 验证技能引用的Buff是否存在
        foreach (var logicConfig in ConfigMgr.SkillLogicConfig.Data.Values)
        {
            ValidateActionConfigReferences(logicConfig.ActionConfig, result, $"技能逻辑 {logicConfig.SkillId}");
        }

        // 验证Buff引用的其他配置是否存在
        foreach (var logicConfig in ConfigMgr.BuffLogicConfig.Data.Values)
        {
            ValidateActionConfigReferences(logicConfig.ActionConfig, result, $"Buff逻辑 {logicConfig.BuffId}");
        }
    }

    private static void ValidateActionConfigReferences(string actionConfig, ValidationResult result, string source)
    {
        if (string.IsNullOrEmpty(actionConfig)) return;

        try
        {
            var actions = ActionConfigParser.ParseActionConfig(actionConfig);
            foreach (var action in actions)
            {
                ValidateActionReferences(action, result, source);
            }
        }
        catch (Exception e)
        {
            result.AddError($"{source}: 动作配置解析失败 - {e.Message}");
        }
    }

    private static void ValidateActionReferences(IAPIAction action, ValidationResult result, string source)
    {
        // 根据动作类型验证引用
        if (action is AddBuffAPIAction addBuffAction)
        {
            // 验证引用的Buff是否存在
            var buffId = GetActionParameter(addBuffAction, "buffId");
            if (!string.IsNullOrEmpty(buffId) && !ConfigMgr.BuffConfig.Data.ContainsKey(buffId))
            {
                result.AddError($"{source}: 引用的Buff不存在 - {buffId}");
            }
        }
        // 可以添加更多动作类型的引用验证
    }

    private static string GetActionParameter(IAPIAction action, string parameterName)
    {
        // 通过反射获取动作的参数值
        var field = action.GetType().GetField(parameterName, BindingFlags.NonPublic | BindingFlags.Instance);
        return field?.GetValue(action) as string ?? "";
    }
}

/// <summary>
/// 验证结果类
/// </summary>
public class ValidationResult
{
    public List<string> Errors { get; } = new List<string>();
    public List<string> Warnings { get; } = new List<string>();
    public List<string> Infos { get; } = new List<string>();

    public bool HasErrors => Errors.Count > 0;
    public bool HasWarnings => Warnings.Count > 0;

    public void AddError(string message)
    {
        Errors.Add(message);
        GameLogManager.Log(message, "ConfigValidator", GameLogManager.LogType.Error);
    }

    public void AddWarning(string message)
    {
        Warnings.Add(message);
        GameLogManager.Log(message, "ConfigValidator", GameLogManager.LogType.Warning);
    }

    public void AddInfo(string message)
    {
        Infos.Add(message);
        GameLogManager.Log(message, "ConfigValidator", GameLogManager.LogType.Info);
    }

    public void AddResult(string context, ValidationResult subResult)
    {
        foreach (var error in subResult.Errors)
        {
            AddError($"{context}: {error}");
        }
        foreach (var warning in subResult.Warnings)
        {
            AddWarning($"{context}: {warning}");
        }
        foreach (var info in subResult.Infos)
        {
            AddInfo($"{context}: {info}");
        }
    }

    public string GetSummary()
    {
        return $"验证完成 - 错误: {Errors.Count}, 警告: {Warnings.Count}, 信息: {Infos.Count}";
    }
}
```

### 15.2 Unity编辑器配置验证工具

```csharp
/// <summary>
/// Unity编辑器配置验证工具
/// </summary>
public class ConfigValidationWindow : EditorWindow
{
    private ValidationResult lastValidationResult;
    private Vector2 scrollPosition;
    private bool showErrors = true;
    private bool showWarnings = true;
    private bool showInfos = false;

    [MenuItem("Tools/GameConfig/配置验证器")]
    public static void ShowWindow()
    {
        GetWindow<ConfigValidationWindow>("配置验证器");
    }

    private void OnGUI()
    {
        EditorGUILayout.LabelField("GameConfig配置验证器", EditorStyles.boldLabel);
        EditorGUILayout.Space();

        // 验证按钮
        if (GUILayout.Button("验证所有配置", GUILayout.Height(30)))
        {
            ValidateConfigs();
        }

        EditorGUILayout.Space();

        // 显示选项
        EditorGUILayout.LabelField("显示选项", EditorStyles.boldLabel);
        showErrors = EditorGUILayout.Toggle("显示错误", showErrors);
        showWarnings = EditorGUILayout.Toggle("显示警告", showWarnings);
        showInfos = EditorGUILayout.Toggle("显示信息", showInfos);

        EditorGUILayout.Space();

        // 显示验证结果
        if (lastValidationResult != null)
        {
            DisplayValidationResult();
        }
    }

    private void ValidateConfigs()
    {
        try
        {
            // 确保配置已加载
            if (!IsConfigLoaded())
            {
                ConfigMgr.Init("Config/Config");
            }

            lastValidationResult = ConfigIntegrityValidator.ValidateAllConfigs();
            
            EditorUtility.DisplayDialog("验证完成", lastValidationResult.GetSummary(), "确定");
        }
        catch (Exception e)
        {
            EditorUtility.DisplayDialog("验证失败", $"配置验证过程中发生错误: {e.Message}", "确定");
        }
    }

    private void DisplayValidationResult()
    {
        EditorGUILayout.LabelField("验证结果", EditorStyles.boldLabel);
        EditorGUILayout.LabelField(lastValidationResult.GetSummary());

        scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);

        // 显示错误
        if (showErrors && lastValidationResult.Errors.Count > 0)
        {
            EditorGUILayout.LabelField("错误", EditorStyles.boldLabel);
            foreach (var error in lastValidationResult.Errors)
            {
                EditorGUILayout.HelpBox(error, MessageType.Error);
            }
        }

        // 显示警告
        if (showWarnings && lastValidationResult.Warnings.Count > 0)
        {
            EditorGUILayout.LabelField("警告", EditorStyles.boldLabel);
            foreach (var warning in lastValidationResult.Warnings)
            {
                EditorGUILayout.HelpBox(warning, MessageType.Warning);
            }
        }

        // 显示信息
        if (showInfos && lastValidationResult.Infos.Count > 0)
        {
            EditorGUILayout.LabelField("信息", EditorStyles.boldLabel);
            foreach (var info in lastValidationResult.Infos)
            {
                EditorGUILayout.HelpBox(info, MessageType.Info);
            }
        }

        EditorGUILayout.EndScrollView();
    }

    private bool IsConfigLoaded()
    {
        try
        {
            return ConfigMgr.SkillConfig != null && ConfigMgr.SkillConfig.Data.Count > 0;
        }
        catch
        {
            return false;
        }
    }
}
```

通过这些详细的实现，GameConfig集成系统现在具备了：

1. **完整的API动作解析器**：支持多种游戏动作的配置化执行
2. **灵活的触发器系统**：可通过配置创建各种游戏触发器
3. **全面的配置验证**：确保配置数据的完整性和正确性
4. **Unity编辑器工具**：提供可视化的配置验证和管理界面
5. **错误处理机制**：完善的错误处理和降级策略

这个系统真正实现了配置与代码的彻底分离，使策划能够通过Excel配置复杂的游戏逻辑，同时保持了系统的性能和稳定性。

## 16. 配置系统性能优化

### 16.1 配置数据缓存策略

```csharp
/// <summary>
/// 配置数据缓存管理器，提供高效的配置数据访问
/// </summary>
public class ConfigCacheManager : MonoBehaviour
{
    private static ConfigCacheManager _instance;
    public static ConfigCacheManager Instance
    {
        get
        {
            if (_instance == null)
            {
                var go = new GameObject("ConfigCacheManager");
                _instance = go.AddComponent<ConfigCacheManager>();
                DontDestroyOnLoad(go);
            }
            return _instance;
        }
    }

    [Header("缓存设置")]
    public int maxCacheSize = 1000;
    public float cacheExpireTime = 300f; // 5分钟

    // 解析结果缓存
    private Dictionary<string, CachedActionConfig> actionConfigCache = new Dictionary<string, CachedActionConfig>();
    private Dictionary<string, CachedTriggerConfig> triggerConfigCache = new Dictionary<string, CachedTriggerConfig>();
    private Dictionary<string, CachedConditionResult> conditionResultCache = new Dictionary<string, CachedConditionResult>();

    // LRU缓存实现
    private LinkedList<string> accessOrder = new LinkedList<string>();
    private Dictionary<string, LinkedListNode<string>> accessNodes = new Dictionary<string, LinkedListNode<string>>();

    private void Start()
    {
        // 定期清理过期缓存
        InvokeRepeating(nameof(CleanExpiredCache), cacheExpireTime, cacheExpireTime);
    }

    /// <summary>
    /// 获取缓存的动作配置
    /// </summary>
    public List<IAPIAction> GetCachedActionConfig(string actionConfig)
    {
        if (string.IsNullOrEmpty(actionConfig))
            return new List<IAPIAction>();

        string cacheKey = $"action_{actionConfig.GetHashCode()}";

        if (actionConfigCache.TryGetValue(cacheKey, out CachedActionConfig cached))
        {
            if (Time.time - cached.CacheTime < cacheExpireTime)
            {
                UpdateAccessOrder(cacheKey);
                return cached.Actions;
            }
            else
            {
                // 缓存过期，移除
                RemoveFromCache(cacheKey);
            }
        }

        // 解析并缓存
        var actions = ActionConfigParser.ParseActionConfig(actionConfig);
        CacheActionConfig(cacheKey, actions);
        return actions;
    }

    /// <summary>
    /// 获取缓存的触发器配置
    /// </summary>
    public ITriggerConfig GetCachedTriggerConfig(string triggerConfig)
    {
        if (string.IsNullOrEmpty(triggerConfig))
            return null;

        string cacheKey = $"trigger_{triggerConfig.GetHashCode()}";

        if (triggerConfigCache.TryGetValue(cacheKey, out CachedTriggerConfig cached))
        {
            if (Time.time - cached.CacheTime < cacheExpireTime)
            {
                UpdateAccessOrder(cacheKey);
                return cached.TriggerConfig;
            }
            else
            {
                RemoveFromCache(cacheKey);
            }
        }

        // 解析并缓存
        var config = TriggerConfigParser.ParseTriggerConfig(triggerConfig);
        CacheTriggerConfig(cacheKey, config);
        return config;
    }

    /// <summary>
    /// 获取缓存的条件评估结果
    /// </summary>
    public bool? GetCachedConditionResult(string condition, LogicExecutionContext context)
    {
        if (string.IsNullOrEmpty(condition))
            return true;

        // 生成上下文相关的缓存键
        string contextKey = GenerateContextKey(context);
        string cacheKey = $"condition_{condition.GetHashCode()}_{contextKey.GetHashCode()}";

        if (conditionResultCache.TryGetValue(cacheKey, out CachedConditionResult cached))
        {
            if (Time.time - cached.CacheTime < 1f) // 条件结果缓存时间较短
            {
                UpdateAccessOrder(cacheKey);
                return cached.Result;
            }
            else
            {
                RemoveFromCache(cacheKey);
            }
        }

        return null; // 缓存未命中
    }

    /// <summary>
    /// 缓存条件评估结果
    /// </summary>
    public void CacheConditionResult(string condition, LogicExecutionContext context, bool result)
    {
        string contextKey = GenerateContextKey(context);
        string cacheKey = $"condition_{condition.GetHashCode()}_{contextKey.GetHashCode()}";

        var cached = new CachedConditionResult
        {
            Result = result,
            CacheTime = Time.time
        };

        conditionResultCache[cacheKey] = cached;
        UpdateAccessOrder(cacheKey);
        EnforceCacheLimit();
    }

    private void CacheActionConfig(string cacheKey, List<IAPIAction> actions)
    {
        var cached = new CachedActionConfig
        {
            Actions = actions,
            CacheTime = Time.time
        };

        actionConfigCache[cacheKey] = cached;
        UpdateAccessOrder(cacheKey);
        EnforceCacheLimit();
    }

    private void CacheTriggerConfig(string cacheKey, ITriggerConfig config)
    {
        var cached = new CachedTriggerConfig
        {
            TriggerConfig = config,
            CacheTime = Time.time
        };

        triggerConfigCache[cacheKey] = cached;
        UpdateAccessOrder(cacheKey);
        EnforceCacheLimit();
    }

    private string GenerateContextKey(LogicExecutionContext context)
    {
        // 生成上下文相关的键，用于条件缓存
        var sb = new System.Text.StringBuilder();
        
        if (context.Target != null)
        {
            sb.Append($"target_{context.Target.GetInstanceID()}");
            // 添加关键属性值
            var healthAttr = context.Target.GetAttribute("Health_Attribute");
            if (healthAttr != null)
                sb.Append($"_h{healthAttr.Value:F1}");
        }

        if (context.Caster != null)
        {
            sb.Append($"_caster_{context.Caster.GetInstanceID()}");
        }

        sb.Append($"_layer{context.Layer}");

        return sb.ToString();
    }

    private void UpdateAccessOrder(string cacheKey)
    {
        if (accessNodes.TryGetValue(cacheKey, out LinkedListNode<string> node))
        {
            accessOrder.Remove(node);
        }

        var newNode = accessOrder.AddFirst(cacheKey);
        accessNodes[cacheKey] = newNode;
    }

    private void EnforceCacheLimit()
    {
        while (accessOrder.Count > maxCacheSize)
        {
            var lastKey = accessOrder.Last.Value;
            RemoveFromCache(lastKey);
        }
    }

    private void RemoveFromCache(string cacheKey)
    {
        actionConfigCache.Remove(cacheKey);
        triggerConfigCache.Remove(cacheKey);
        conditionResultCache.Remove(cacheKey);

        if (accessNodes.TryGetValue(cacheKey, out LinkedListNode<string> node))
        {
            accessOrder.Remove(node);
            accessNodes.Remove(cacheKey);
        }
    }

    private void CleanExpiredCache()
    {
        var currentTime = Time.time;
        var keysToRemove = new List<string>();

        // 检查动作配置缓存
        foreach (var kvp in actionConfigCache)
        {
            if (currentTime - kvp.Value.CacheTime > cacheExpireTime)
            {
                keysToRemove.Add(kvp.Key);
            }
        }

        // 检查触发器配置缓存
        foreach (var kvp in triggerConfigCache)
        {
            if (currentTime - kvp.Value.CacheTime > cacheExpireTime)
            {
                keysToRemove.Add(kvp.Key);
            }
        }

        // 检查条件结果缓存（较短的过期时间）
        foreach (var kvp in conditionResultCache)
        {
            if (currentTime - kvp.Value.CacheTime > 1f)
            {
                keysToRemove.Add(kvp.Key);
            }
        }

        // 移除过期缓存
        foreach (var key in keysToRemove)
        {
            RemoveFromCache(key);
        }

        if (keysToRemove.Count > 0)
        {
            GameLogManager.Log($"清理了 {keysToRemove.Count} 个过期缓存项", "ConfigCacheManager");
        }
    }

    /// <summary>
    /// 清空所有缓存
    /// </summary>
    public void ClearAllCache()
    {
        actionConfigCache.Clear();
        triggerConfigCache.Clear();
        conditionResultCache.Clear();
        accessOrder.Clear();
        accessNodes.Clear();
        
        GameLogManager.Log("已清空所有配置缓存", "ConfigCacheManager");
    }

    /// <summary>
    /// 获取缓存统计信息
    /// </summary>
    public CacheStatistics GetCacheStatistics()
    {
        return new CacheStatistics
        {
            ActionConfigCacheCount = actionConfigCache.Count,
            TriggerConfigCacheCount = triggerConfigCache.Count,
            ConditionResultCacheCount = conditionResultCache.Count,
            TotalCacheCount = accessOrder.Count,
            MaxCacheSize = maxCacheSize
        };
    }
}

// 缓存数据结构
public class CachedActionConfig
{
    public List<IAPIAction> Actions;
    public float CacheTime;
}

public class CachedTriggerConfig
{
    public ITriggerConfig TriggerConfig;
    public float CacheTime;
}

public class CachedConditionResult
{
    public bool Result;
    public float CacheTime;
}

public class CacheStatistics
{
    public int ActionConfigCacheCount;
    public int TriggerConfigCacheCount;
    public int ConditionResultCacheCount;
    public int TotalCacheCount;
    public int MaxCacheSize;

    public float CacheUsagePercentage => (float)TotalCacheCount / MaxCacheSize * 100f;
}
```

### 16.2 性能监控系统

```csharp
/// <summary>
/// 配置系统性能监控器
/// </summary>
public class ConfigPerformanceMonitor : MonoBehaviour
{
    private static ConfigPerformanceMonitor _instance;
    public static ConfigPerformanceMonitor Instance
    {
        get
        {
            if (_instance == null)
            {
                var go = new GameObject("ConfigPerformanceMonitor");
                _instance = go.AddComponent<ConfigPerformanceMonitor>();
                DontDestroyOnLoad(go);
            }
            return _instance;
        }
    }

    [Header("监控设置")]
    public bool enableMonitoring = true;
    public float reportInterval = 10f;
    public int maxRecordCount = 1000;

    // 性能记录
    private List<PerformanceRecord> records = new List<PerformanceRecord>();
    private Dictionary<string, PerformanceStats> operationStats = new Dictionary<string, PerformanceStats>();

    private void Start()
    {
        if (enableMonitoring)
        {
            InvokeRepeating(nameof(GeneratePerformanceReport), reportInterval, reportInterval);
        }
    }

    /// <summary>
    /// 记录操作性能
    /// </summary>
    public void RecordOperation(string operationType, string operationName, float executionTime, bool success = true)
    {
        if (!enableMonitoring) return;

        var record = new PerformanceRecord
        {
            OperationType = operationType,
            OperationName = operationName,
            ExecutionTime = executionTime,
            Success = success,
            Timestamp = Time.time
        };

        records.Add(record);

        // 更新统计信息
        string statsKey = $"{operationType}_{operationName}";
        if (!operationStats.TryGetValue(statsKey, out PerformanceStats stats))
        {
            stats = new PerformanceStats { OperationType = operationType, OperationName = operationName };
            operationStats[statsKey] = stats;
        }

        stats.TotalExecutions++;
        stats.TotalExecutionTime += executionTime;
        stats.AverageExecutionTime = stats.TotalExecutionTime / stats.TotalExecutions;

        if (executionTime > stats.MaxExecutionTime)
        {
            stats.MaxExecutionTime = executionTime;
        }

        if (stats.MinExecutionTime == 0 || executionTime < stats.MinExecutionTime)
        {
            stats.MinExecutionTime = executionTime;
        }

        if (success)
        {
            stats.SuccessCount++;
        }
        else
        {
            stats.FailureCount++;
        }

        // 限制记录数量
        if (records.Count > maxRecordCount)
        {
            records.RemoveRange(0, records.Count - maxRecordCount);
        }
    }

    /// <summary>
    /// 生成性能报告
    /// </summary>
    private void GeneratePerformanceReport()
    {
        if (operationStats.Count == 0) return;

        var report = new System.Text.StringBuilder();
        report.AppendLine("=== 配置系统性能报告 ===");
        report.AppendLine($"报告时间: {System.DateTime.Now:yyyy-MM-dd HH:mm:ss}");
        report.AppendLine($"监控间隔: {reportInterval}秒");
        report.AppendLine();

        // 按操作类型分组统计
        var groupedStats = operationStats.Values.GroupBy(s => s.OperationType);

        foreach (var group in groupedStats)
        {
            report.AppendLine($"操作类型: {group.Key}");
            report.AppendLine("----------------------------------------");

            foreach (var stats in group.OrderByDescending(s => s.TotalExecutions))
            {
                report.AppendLine($"  操作: {stats.OperationName}");
                report.AppendLine($"    执行次数: {stats.TotalExecutions}");
                report.AppendLine($"    成功率: {(stats.SuccessCount / (float)stats.TotalExecutions * 100):F1}%");
                report.AppendLine($"    平均耗时: {stats.AverageExecutionTime * 1000:F2}ms");
                report.AppendLine($"    最大耗时: {stats.MaxExecutionTime * 1000:F2}ms");
                report.AppendLine($"    最小耗时: {stats.MinExecutionTime * 1000:F2}ms");
                report.AppendLine();
            }
        }

        // 缓存统计
        var cacheStats = ConfigCacheManager.Instance.GetCacheStatistics();
        report.AppendLine("缓存统计:");
        report.AppendLine($"  动作配置缓存: {cacheStats.ActionConfigCacheCount}");
        report.AppendLine($"  触发器配置缓存: {cacheStats.TriggerConfigCacheCount}");
        report.AppendLine($"  条件结果缓存: {cacheStats.ConditionResultCacheCount}");
        report.AppendLine($"  缓存使用率: {cacheStats.CacheUsagePercentage:F1}%");

        GameLogManager.Log(report.ToString(), "ConfigPerformanceMonitor");
    }

    /// <summary>
    /// 获取性能统计信息
    /// </summary>
    public Dictionary<string, PerformanceStats> GetPerformanceStats()
    {
        return new Dictionary<string, PerformanceStats>(operationStats);
    }

    /// <summary>
    /// 清空性能记录
    /// </summary>
    public void ClearPerformanceRecords()
    {
        records.Clear();
        operationStats.Clear();
        GameLogManager.Log("已清空性能记录", "ConfigPerformanceMonitor");
    }

    /// <summary>
    /// 获取最近的性能记录
    /// </summary>
    public List<PerformanceRecord> GetRecentRecords(int count = 100)
    {
        int startIndex = Mathf.Max(0, records.Count - count);
        return records.GetRange(startIndex, records.Count - startIndex);
    }
}

// 性能记录数据结构
public class PerformanceRecord
{
    public string OperationType;
    public string OperationName;
    public float ExecutionTime;
    public bool Success;
    public float Timestamp;
}

public class PerformanceStats
{
    public string OperationType;
    public string OperationName;
    public int TotalExecutions;
    public int SuccessCount;
    public int FailureCount;
    public float TotalExecutionTime;
    public float AverageExecutionTime;
    public float MaxExecutionTime;
    public float MinExecutionTime;
}
```

### 16.3 优化的配置解析器

```csharp
/// <summary>
/// 优化的动作配置解析器，集成性能监控和缓存
/// </summary>
public static class OptimizedActionConfigParser
{
    /// <summary>
    /// 解析动作配置（带性能监控和缓存）
    /// </summary>
    public static List<IAPIAction> ParseActionConfig(string actionConfig)
    {
        if (string.IsNullOrEmpty(actionConfig))
            return new List<IAPIAction>();

        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        bool success = true;
        List<IAPIAction> result = null;

        try
        {
            // 尝试从缓存获取
            result = ConfigCacheManager.Instance.GetCachedActionConfig(actionConfig);
            
            if (result != null)
            {
                stopwatch.Stop();
                ConfigPerformanceMonitor.Instance.RecordOperation(
                    "ActionConfig", "ParseFromCache", stopwatch.ElapsedMilliseconds / 1000f, true);
                return result;
            }

            // 缓存未命中，执行解析
            result = ActionConfigParser.ParseActionConfig(actionConfig);
        }
        catch (Exception e)
        {
            success = false;
            GameLogManager.Log($"解析动作配置失败: {actionConfig}, 错误: {e.Message}", 
                             "OptimizedActionConfigParser", GameLogManager.LogType.Error);
            result = new List<IAPIAction>();
        }
        finally
        {
            stopwatch.Stop();
            ConfigPerformanceMonitor.Instance.RecordOperation(
                "ActionConfig", "Parse", stopwatch.ElapsedMilliseconds / 1000f, success);
        }

        return result ?? new List<IAPIAction>();
    }
}

/// <summary>
/// 优化的条件评估器，集成性能监控和缓存
/// </summary>
public static class OptimizedConditionEvaluator
{
    /// <summary>
    /// 评估条件（带性能监控和缓存）
    /// </summary>
    public static bool EvaluateCondition(string condition, LogicExecutionContext context)
    {
        if (string.IsNullOrEmpty(condition)) return true;

        var stopwatch = System.Diagnostics.Stopwatch.StartNew();
        bool success = true;
        bool result = false;

        try
        {
            // 尝试从缓存获取结果
            var cachedResult = ConfigCacheManager.Instance.GetCachedConditionResult(condition, context);
            if (cachedResult.HasValue)
            {
                stopwatch.Stop();
                ConfigPerformanceMonitor.Instance.RecordOperation(
                    "Condition", "EvaluateFromCache", stopwatch.ElapsedMilliseconds / 1000f, true);
                return cachedResult.Value;
            }

            // 缓存未命中，执行评估
            result = ConditionEvaluator.EvaluateCondition(condition, context);
            
            // 缓存结果
            ConfigCacheManager.Instance.CacheConditionResult(condition, context, result);
        }
        catch (Exception e)
        {
            success = false;
            GameLogManager.Log($"评估条件失败: {condition}, 错误: {e.Message}", 
                             "OptimizedConditionEvaluator", GameLogManager.LogType.Error);
            result = false;
        }
        finally
        {
            stopwatch.Stop();
            ConfigPerformanceMonitor.Instance.RecordOperation(
                "Condition", "Evaluate", stopwatch.ElapsedMilliseconds / 1000f, success);
        }

        return result;
    }
}
```

### 16.4 Unity编辑器性能监控窗口

```csharp
/// <summary>
/// Unity编辑器性能监控窗口
/// </summary>
public class ConfigPerformanceWindow : EditorWindow
{
    private Vector2 scrollPosition;
    private bool autoRefresh = true;
    private float refreshInterval = 1f;
    private double lastRefreshTime;

    [MenuItem("Tools/GameConfig/性能监控")]
    public static void ShowWindow()
    {
        GetWindow<ConfigPerformanceWindow>("配置性能监控");
    }

    private void OnGUI()
    {
        EditorGUILayout.LabelField("GameConfig性能监控", EditorStyles.boldLabel);
        EditorGUILayout.Space();

        // 控制面板
        DrawControlPanel();
        EditorGUILayout.Space();

        // 性能统计
        DrawPerformanceStats();
        EditorGUILayout.Space();

        // 缓存统计
        DrawCacheStats();

        // 自动刷新
        if (autoRefresh && EditorApplication.timeSinceStartup - lastRefreshTime > refreshInterval)
        {
            lastRefreshTime = EditorApplication.timeSinceStartup;
            Repaint();
        }
    }

    private void DrawControlPanel()
    {
        EditorGUILayout.LabelField("控制面板", EditorStyles.boldLabel);
        
        EditorGUILayout.BeginHorizontal();
        autoRefresh = EditorGUILayout.Toggle("自动刷新", autoRefresh);
        refreshInterval = EditorGUILayout.FloatField("刷新间隔(秒)", refreshInterval);
        EditorGUILayout.EndHorizontal();

        EditorGUILayout.BeginHorizontal();
        if (GUILayout.Button("清空性能记录"))
        {
            if (Application.isPlaying)
            {
                ConfigPerformanceMonitor.Instance.ClearPerformanceRecords();
            }
        }

        if (GUILayout.Button("清空缓存"))
        {
            if (Application.isPlaying)
            {
                ConfigCacheManager.Instance.ClearAllCache();
            }
        }

        if (GUILayout.Button("手动刷新"))
        {
            Repaint();
        }
        EditorGUILayout.EndHorizontal();
    }

    private void DrawPerformanceStats()
    {
        EditorGUILayout.LabelField("性能统计", EditorStyles.boldLabel);

        if (!Application.isPlaying)
        {
            EditorGUILayout.HelpBox("需要在运行时查看性能统计", MessageType.Info);
            return;
        }

        var stats = ConfigPerformanceMonitor.Instance.GetPerformanceStats();
        if (stats.Count == 0)
        {
            EditorGUILayout.LabelField("暂无性能数据");
            return;
        }

        scrollPosition = EditorGUILayout.BeginScrollView(scrollPosition);

        // 按操作类型分组显示
        var groupedStats = stats.Values.GroupBy(s => s.OperationType);

        foreach (var group in groupedStats)
        {
            EditorGUILayout.LabelField($"操作类型: {group.Key}", EditorStyles.boldLabel);
            
            foreach (var stat in group.OrderByDescending(s => s.TotalExecutions))
            {
                EditorGUILayout.BeginVertical("box");
                
                EditorGUILayout.LabelField($"操作: {stat.OperationName}", EditorStyles.boldLabel);
                EditorGUILayout.LabelField($"执行次数: {stat.TotalExecutions}");
                EditorGUILayout.LabelField($"成功率: {(stat.SuccessCount / (float)stat.TotalExecutions * 100):F1}%");
                EditorGUILayout.LabelField($"平均耗时: {stat.AverageExecutionTime * 1000:F2}ms");
                EditorGUILayout.LabelField($"最大耗时: {stat.MaxExecutionTime * 1000:F2}ms");
                EditorGUILayout.LabelField($"最小耗时: {stat.MinExecutionTime * 1000:F2}ms");
                
                // 性能警告
                if (stat.AverageExecutionTime > 0.01f) // 超过10ms
                {
                    EditorGUILayout.HelpBox("平均执行时间较长，建议优化", MessageType.Warning);
                }
                
                if (stat.FailureCount > 0)
                {
                    EditorGUILayout.HelpBox($"存在 {stat.FailureCount} 次失败", MessageType.Error);
                }

                EditorGUILayout.EndVertical();
                EditorGUILayout.Space();
            }
        }

        EditorGUILayout.EndScrollView();
    }

    private void DrawCacheStats()
    {
        EditorGUILayout.LabelField("缓存统计", EditorStyles.boldLabel);

        if (!Application.isPlaying)
        {
            EditorGUILayout.HelpBox("需要在运行时查看缓存统计", MessageType.Info);
            return;
        }

        var cacheStats = ConfigCacheManager.Instance.GetCacheStatistics();

        EditorGUILayout.BeginVertical("box");
        EditorGUILayout.LabelField($"动作配置缓存: {cacheStats.ActionConfigCacheCount}");
        EditorGUILayout.LabelField($"触发器配置缓存: {cacheStats.TriggerConfigCacheCount}");
        EditorGUILayout.LabelField($"条件结果缓存: {cacheStats.ConditionResultCacheCount}");
        EditorGUILayout.LabelField($"总缓存数量: {cacheStats.TotalCacheCount} / {cacheStats.MaxCacheSize}");
        
        // 缓存使用率进度条
        var rect = EditorGUILayout.GetControlRect();
        EditorGUI.ProgressBar(rect, cacheStats.CacheUsagePercentage / 100f, $"缓存使用率: {cacheStats.CacheUsagePercentage:F1}%");
        
        // 缓存使用率警告
        if (cacheStats.CacheUsagePercentage > 80f)
        {
            EditorGUILayout.HelpBox("缓存使用率较高，建议增加缓存大小或清理缓存", MessageType.Warning);
        }
        
        EditorGUILayout.EndVertical();
    }
}
```

## 17. 配置系统扩展指南

### 17.1 添加新的API动作类型

```csharp
/// <summary>
/// 自定义API动作示例：传送单位
/// 配置格式：TeleportUnit:target=self,position=10,0,5,playEffect=true
/// </summary>
public class TeleportUnitAPIAction : APIActionBase
{
    private string targetType;
    private Vector3 position;
    private bool playEffect;

    public override void Initialize(Dictionary<string, string> parameters)
    {
        base.Initialize(parameters);
        
        targetType = GetStringParameter("target", "self");
        playEffect = GetBoolParameter("playEffect", true);
        
        // 解析位置参数
        string positionStr = GetStringParameter("position", "0,0,0");
        position = ParseVector3(positionStr);
    }

    public override void Execute(APIExecutionContext context)
    {
        var targetUnit = GetTargetUnit(targetType, context);
        if (targetUnit == null)
        {
            GameLogManager.Log($"TeleportUnit动作找不到目标单位: {targetType}", 
                             "TeleportUnitAPIAction", GameLogManager.LogType.Warning);
            return;
        }

        // 执行传送
        targetUnit.transform.position = position;

        // 播放传送特效
        if (playEffect)
        {
            PlayTeleportEffect(targetUnit.transform.position);
        }

        GameLogManager.Log($"传送单位 {targetUnit.name} 到位置 {position}", "TeleportUnitAPIAction");
    }

    private void PlayTeleportEffect(Vector3 position)
    {
        var effectPrefab = Resources.Load<GameObject>("FX/TeleportEffect");
        if (effectPrefab != null)
        {
            var effect = Object.Instantiate(effectPrefab, position, Quaternion.identity);
            Object.Destroy(effect, 2f);
        }
    }

    private Vector3 ParseVector3(string vectorStr)
    {
        var parts = vectorStr.Split(',');
        if (parts.Length == 3)
        {
            if (float.TryParse(parts[0], out float x) &&
                float.TryParse(parts[1], out float y) &&
                float.TryParse(parts[2], out float z))
            {
                return new Vector3(x, y, z);
            }
        }
        return Vector3.zero;
    }

    public override bool ValidateParameters()
    {
        return base.ValidateParameters() && !string.IsNullOrEmpty(targetType);
    }
}

/// <summary>
/// 注册自定义API动作的初始化器
/// </summary>
[InitializeOnLoad]
public static class CustomAPIActionRegistrar
{
    static CustomAPIActionRegistrar()
    {
        // 注册自定义API动作类型
        ActionConfigParser.RegisterActionType("TeleportUnit", typeof(TeleportUnitAPIAction));
        ActionConfigParser.RegisterActionType("SpawnUnit", typeof(SpawnUnitAPIAction));
        ActionConfigParser.RegisterActionType("ModifySkillCooldown", typeof(ModifySkillCooldownAPIAction));
        
        GameLogManager.Log("已注册自定义API动作类型", "CustomAPIActionRegistrar");
    }
}
```

### 17.2 添加新的触发器类型

```csharp
/// <summary>
/// 自定义触发器示例：距离触发器
/// 配置格式：DistanceTrigger:distance=5.0,target=enemy,action=AddBuff
/// </summary>
public class DistanceTriggerConfig : ITriggerConfig
{
    private float triggerDistance;
    private string targetType;
    private string action;

    public void Initialize(Dictionary<string, string> parameters)
    {
        triggerDistance = GetFloatParameter(parameters, "distance", 5f);
        targetType = GetStringParameter(parameters, "target", "enemy");
        action = GetStringParameter(parameters, "action", "");
    }

    public void CreateTrigger(Unit targetUnit, object source = null)
    {
        // 创建距离触发器组件
        var triggerGO = new GameObject($"DistanceTrigger_{targetUnit.name}");
        triggerGO.transform.SetParent(targetUnit.transform);
        
        var distanceTrigger = triggerGO.AddComponent<DistanceTriggerComponent>();
        distanceTrigger.Initialize(targetUnit, triggerDistance, targetType, action);

        GameLogManager.Log($"创建距离触发器: 距离={triggerDistance}, 目标={targetType}", "DistanceTriggerConfig");
    }

    public bool ValidateParameters()
    {
        return triggerDistance > 0;
    }

    private float GetFloatParameter(Dictionary<string, string> parameters, string key, float defaultValue)
    {
        if (parameters.TryGetValue(key, out string value) && float.TryParse(value, out float result))
        {
            return result;
        }
        return defaultValue;
    }

    private string GetStringParameter(Dictionary<string, string> parameters, string key, string defaultValue)
    {
        return parameters.TryGetValue(key, out string value) ? value : defaultValue;
    }
}

/// <summary>
/// 距离触发器组件
/// </summary>
public class DistanceTriggerComponent : MonoBehaviour
{
    private Unit ownerUnit;
    private float triggerDistance;
    private string targetType;
    private string action;
    private bool hasTriggered = false;

    public void Initialize(Unit owner, float distance, string target, string actionConfig)
    {
        ownerUnit = owner;
        triggerDistance = distance;
        targetType = target;
        action = actionConfig;
    }

    private void Update()
    {
        if (hasTriggered || ownerUnit == null) return;

        // 检查距离条件
        if (CheckDistanceCondition())
        {
            ExecuteTriggerAction();
            hasTriggered = true;
        }
    }

    private bool CheckDistanceCondition()
    {
        // 根据目标类型查找目标单位
        var targets = FindTargetUnits();
        
        foreach (var target in targets)
        {
            float distance = Vector3.Distance(ownerUnit.transform.position, target.transform.position);
            if (distance <= triggerDistance)
            {
                return true;
            }
        }

        return false;
    }

    private List<Unit> FindTargetUnits()
    {
        var targets = new List<Unit>();
        var allUnits = FindObjectsOfType<Unit>();

        foreach (var unit in allUnits)
        {
            if (unit == ownerUnit) continue;

            // 根据目标类型筛选
            if (IsValidTarget(unit))
            {
                targets.Add(unit);
            }
        }

        return targets;
    }

    private bool IsValidTarget(Unit unit)
    {
        switch (targetType.ToLower())
        {
            case "enemy":
                return UnitRelationshipUtil.IsHostile(ownerUnit, unit);
            case "ally":
                return UnitRelationshipUtil.IsFriendly(ownerUnit, unit);
            case "any":
                return true;
            default:
                return false;
        }
    }

    private void ExecuteTriggerAction()
    {
        if (string.IsNullOrEmpty(action)) return;

        var context = new APIExecutionContext
        {
            Caster = ownerUnit.gameObject,
            Target = ownerUnit.gameObject
        };

        var actions = OptimizedActionConfigParser.ParseActionConfig(action);
        foreach (var apiAction in actions)
        {
            apiAction.Execute(context);
        }

        GameLogManager.Log($"距离触发器激活: {ownerUnit.name}", "DistanceTriggerComponent");
    }
}
```

### 17.3 配置系统扩展最佳实践

```csharp
/// <summary>
/// 配置系统扩展管理器
/// </summary>
public static class ConfigExtensionManager
{
    private static bool isInitialized = false;

    /// <summary>
    /// 初始化所有扩展
    /// </summary>
    [RuntimeInitializeOnLoadMethod(RuntimeInitializeLoadType.BeforeSceneLoad)]
    public static void InitializeExtensions()
    {
        if (isInitialized) return;

        // 注册自定义API动作
        RegisterCustomAPIActions();

        // 注册自定义触发器
        RegisterCustomTriggers();

        // 注册自定义条件评估器
        RegisterCustomConditionEvaluators();

        isInitialized = true;
        GameLogManager.Log("配置系统扩展初始化完成", "ConfigExtensionManager");
    }

    private static void RegisterCustomAPIActions()
    {
        // 注册项目特定的API动作
        ActionConfigParser.RegisterActionType("TeleportUnit", typeof(TeleportUnitAPIAction));
        ActionConfigParser.RegisterActionType("SpawnUnit", typeof(SpawnUnitAPIAction));
        ActionConfigParser.RegisterActionType("ModifySkillCooldown", typeof(ModifySkillCooldownAPIAction));
        ActionConfigParser.RegisterActionType("CreateProjectile", typeof(CreateProjectileAPIAction));
        ActionConfigParser.RegisterActionType("ModifyAttribute", typeof(ModifyAttributeAPIAction));
    }

    private static void RegisterCustomTriggers()
    {
        // 注册项目特定的触发器
        TriggerConfigParser.RegisterTriggerType("DistanceTrigger", typeof(DistanceTriggerConfig));
        TriggerConfigParser.RegisterTriggerType("TimeTrigger", typeof(TimeTriggerConfig));
        TriggerConfigParser.RegisterTriggerType("ComboTrigger", typeof(ComboTriggerConfig));
        TriggerConfigParser.RegisterTriggerType("AreaTrigger", typeof(AreaTriggerConfig));
    }

    private static void RegisterCustomConditionEvaluators()
    {
        // 可以在这里注册自定义的条件评估函数
        // 例如：ConditionEvaluator.RegisterCustomFunction("IsInCombat", IsInCombatFunction);
    }

    /// <summary>
    /// 验证所有扩展是否正确注册
    /// </summary>
    public static bool ValidateExtensions()
    {
        bool isValid = true;

        // 验证API动作注册
        var requiredActions = new[] { "TeleportUnit", "SpawnUnit", "ModifySkillCooldown" };
        foreach (var action in requiredActions)
        {
            if (!IsAPIActionRegistered(action))
            {
                GameLogManager.Log($"API动作未注册: {action}", "ConfigExtensionManager", GameLogManager.LogType.Error);
                isValid = false;
            }
        }

        // 验证触发器注册
        var requiredTriggers = new[] { "DistanceTrigger", "TimeTrigger" };
        foreach (var trigger in requiredTriggers)
        {
            if (!IsTriggerRegistered(trigger))
            {
                GameLogManager.Log($"触发器未注册: {trigger}", "ConfigExtensionManager", GameLogManager.LogType.Error);
                isValid = false;
            }
        }

        return isValid;
    }

    private static bool IsAPIActionRegistered(string actionName)
    {
        // 这里需要访问ActionConfigParser的内部注册表
        // 可以通过反射或提供公共API来实现
        return true; // 简化实现
    }

    private static bool IsTriggerRegistered(string triggerName)
    {
        // 这里需要访问TriggerConfigParser的内部注册表
        return true; // 简化实现
    }
}
```

通过这些扩展，GameConfig集成系统现在具备了：

1. **完整的性能监控**：实时监控配置系统的性能表现
2. **智能缓存机制**：提高配置解析和执行的效率
3. **可扩展架构**：轻松添加新的API动作和触发器类型
4. **Unity编辑器集成**：提供可视化的性能监控和管理工具
5. **最佳实践指南**：确保扩展的质量和一致性

这个完整的配置系统不仅实现了数据与代码的分离，还提供了强大的性能优化和扩展能力，真正做到了企业级的配置管理解决方案。
















