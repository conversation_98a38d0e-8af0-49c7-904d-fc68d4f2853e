# GameConfig使用指南

本文档详细介绍GameConfig系统生成的文件以及如何使用它们。

## 目录
1. [生成的文件概览](#生成的文件概览)
2. [文件详细说明](#文件详细说明)
3. [使用方法](#使用方法)
4. [配置数据类型](#配置数据类型)
5. [最佳实践](#最佳实践)

## 生成的文件概览

GameConfig系统会根据Excel配置表生成以下文件：

### 脚本文件 (Assets/Scripts/GameConfig/Runtime/)
- `ConfigMgr.cs` - 配置管理器，提供初始化和访问接口
- `KVConfig.cs` - 键值对配置类，对应Excel中的KV配置
- `BaseConfig.cs` - 基础配置类，用于表格类配置
- `ConfigUtility.cs` - 配置工具类，提供数据解析功能

### 数据文件 (Assets/Resources/Config/)
- `Config.txt` - 编码后的配置数据文件

### 配置表文件 (Assets/StreamingAssets/GameConfig/)
- `*.xlsx` - Excel配置表文件

## 文件详细说明

### 1. ConfigMgr.cs - 配置管理器
```csharp
public static class ConfigMgr
{
    public static KVConfig KVConfig { private set; get; }
    
    public static void Init(string configPath)
    public static void Parse(string configText)
}
```

**功能**：
- 提供配置系统的初始化接口
- 管理所有配置对象的访问
- 解析配置数据并创建配置对象

### 2. KVConfig.cs - 键值对配置
```csharp
public class KVConfig
{
    public string GameName { get; }      // 游戏名称
    public string Version { get; }       // 版本号
    public IReadOnlyList<int> A { get; } // 整数数组
    // ... 其他配置属性
}
```

**功能**：
- 存储键值对类型的配置数据
- 提供类型安全的属性访问
- 支持各种数据类型（字符串、整数、布尔值、数组等）

### 3. BaseConfig.cs - 基础配置类
```csharp
public class BaseConfig<T, W>
{
    public IReadOnlyDictionary<T, W> Data { get; }
    
    public W Get(T key)
    public W Get(T key, bool ifNullThrowException)
}
```

**功能**：
- 为表格类配置提供基础功能
- 支持泛型键值对访问
- 提供安全的数据获取方法

### 4. ConfigUtility.cs - 配置工具类
```csharp
public static class ConfigUtility
{
    public static string DecodeBase64(string base64String)
    public static int ParseInt(string value)
    public static bool ParseBool(string value)
    public static IReadOnlyList<int> ParseIntList(string value)
    // ... 其他解析方法
}
```

**功能**：
- 提供Base64解码功能
- 提供各种数据类型的解析方法
- 支持数组和复杂数据结构的解析

## 使用方法

### 1. 初始化配置系统
```csharp
using GameConfig;

void Start()
{
    // 初始化配置系统
    ConfigMgr.Init("Config/Config");
}
```

### 2. 访问配置数据
```csharp
// 获取基本配置
string gameName = ConfigMgr.KVConfig.GameName;
string version = ConfigMgr.KVConfig.Version;

// 获取数组配置
var intArray = ConfigMgr.KVConfig.A;
var stringArray = ConfigMgr.KVConfig.C;

// 获取数值配置
int value = ConfigMgr.KVConfig.D;
bool flag = ConfigMgr.KVConfig.F;
```

### 3. 使用示例脚本
将`ConfigUsageExample.cs`脚本添加到场景中的GameObject上：
```csharp
public class ConfigUsageExample : MonoBehaviour
{
    void Start()
    {
        // 自动初始化并展示配置数据
    }
    
    [ContextMenu("显示配置信息")]
    public void ShowConfigInfo()
    {
        // 在Inspector中右键点击可显示配置信息
    }
}
```

## 配置数据类型

GameConfig支持以下数据类型：

### 基础类型
- `string` - 字符串
- `int` - 整数
- `float` - 浮点数
- `bool` - 布尔值

### 集合类型
- `IReadOnlyList<T>` - 一维数组
- `IReadOnlyList<IReadOnlyList<T>>` - 二维数组
- `IReadOnlyDictionary<K, V>` - 字典

### 示例配置
```csharp
// 字符串配置
string gameName = ConfigMgr.KVConfig.GameName; // "Hello World"

// 数组配置
var intArray = ConfigMgr.KVConfig.A; // [35, 0, 0]
var stringArray = ConfigMgr.KVConfig.C; // ["aaa", "bbb", "ccc"]

// 二维数组配置
var matrix = ConfigMgr.KVConfig.B; // [[0,25,-23], [], [3,6]]
```

## 最佳实践

### 1. 初始化时机
```csharp
// 推荐在游戏启动时初始化
public class GameManager : MonoBehaviour
{
    void Awake()
    {
        ConfigMgr.Init("Config/Config");
    }
}
```

### 2. 错误处理
```csharp
try
{
    ConfigMgr.Init("Config/Config");
    Debug.Log("配置加载成功");
}
catch (System.Exception ex)
{
    Debug.LogError($"配置加载失败: {ex.Message}");
}
```

### 3. 配置访问
```csharp
// 安全的配置访问
if (ConfigMgr.KVConfig != null)
{
    string gameName = ConfigMgr.KVConfig.GameName;
    // 使用配置数据
}
else
{
    Debug.LogError("配置未初始化");
}
```

### 4. 性能考虑
```csharp
// 缓存频繁访问的配置
public class PlayerController : MonoBehaviour
{
    private float moveSpeed;
    
    void Start()
    {
        // 缓存配置值，避免重复访问
        moveSpeed = ConfigMgr.KVConfig.PlayerMoveSpeed;
    }
}
```

## 配置文件结构

### Excel配置表格式
- **KV配置**：键值对形式，适合全局设置
- **表格配置**：行列形式，适合数据列表

### 生成的数据文件
- `Config.txt`：Base64编码的配置数据
- 包含所有配置表的合并数据
- 运行时解码并解析为配置对象

## 扩展配置

### 添加新的配置表
1. 在Excel中创建新的配置表
2. 运行配置生成器
3. 系统会自动生成对应的配置类

### 修改现有配置
1. 修改Excel配置表
2. 重新生成配置
3. 配置类会自动更新

## 调试和测试

### 查看配置数据
```csharp
// 使用ConfigUsageExample脚本
// 在Inspector中右键选择"显示配置信息"
```

### 验证配置加载
```csharp
Debug.Log($"配置是否加载: {ConfigMgr.KVConfig != null}");
Debug.Log($"游戏名称: {ConfigMgr.KVConfig?.GameName}");
```

## 常见问题

**Q: 配置数据在哪里？**
A: 配置数据存储在`Assets/Resources/Config/Config.txt`中，以Base64编码格式保存。

**Q: 如何添加新的配置项？**
A: 在Excel配置表中添加新的行或列，然后重新生成配置即可。

**Q: 配置数据是否可以运行时修改？**
A: 生成的配置对象是只读的，不支持运行时修改。如需动态配置，请使用其他系统。

**Q: 如何处理配置加载失败？**
A: 使用try-catch包装初始化代码，并提供合适的错误处理和默认值。 