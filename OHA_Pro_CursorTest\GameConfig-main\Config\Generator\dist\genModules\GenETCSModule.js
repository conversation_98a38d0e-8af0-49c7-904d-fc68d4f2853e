"use strict";var e=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(exports,"__esModule",{value:!0}),exports.GenETCSModule=void 0;const F=e(require("cli-color")),O=e(require("path")),q=require("../utils/IOUtils"),B=require("../utils/StrUtils"),b=require("../DataModel"),a=e(require("fs")),E=require("../utils/CommonUtils"),P=require("../CSTypeEnum"),j=require("../utils/LineBreak"),A=require("../utils/CodeWriter"),g=e(require("crypto-js/enc-base64")),c=e(require("crypto-js/enc-utf8"));class t{static get Instance(){return null==this._instance&&(this._instance=new t),this._instance}gen(t){this._export=b.DataModel.Instance.config.exports.find(e=>e.id==t),this._codeLang=this._export.code_language,console.log(`
================================= 开始生成 ${this._export.id} 配置 =================================
`),q.IOUtils.deleteFolderFileByCondition(this._export.export_script_url,e=>{if(".meta"!=O.default.extname(e))return!0}),q.IOUtils.deleteFolderFileByCondition(this._export.export_com_url,e=>{if(".meta"!=O.default.extname(e))return!0}),q.IOUtils.makeDir(this._export.export_script_url),q.IOUtils.makeDir(this._export.export_com_url),q.IOUtils.copy(`templates/${this._export.template_name||this._export.id}/scripts/`,this._export.export_com_url),this._configNames=b.DataModel.Instance.getConfigNamesAndCutDataByConfigType(this._export.id),this._configSplitor=b.DataModel.Instance.config.export_data_splitor,b.DataModel.Instance.config.export_data_splitor_random_enabled&&(this._configSplitor=B.StrUtils.genPassword(8,!0,!0,!1,!0));let e=this.genEnum();return(e=(e=e&&this.genItemAndVertical())&&this.genMgr())&&(q.IOUtils.deleteFolderFileByCondition(this._export.export_script_url,t=>{if(".meta"==O.default.extname(t)){var n=O.default.dirname(t);let e=O.default.basename(t);n=O.default.join(n,e.split(".")[0]+"."+this._export.script_suffix);return a.default.existsSync(n)?void 0:(console.log(t,"已被删除"),!0)}}),q.IOUtils.deleteFolderFileByCondition(this._export.export_com_url,t=>{if(".meta"==O.default.extname(t)){var n=O.default.dirname(t);let e=O.default.basename(t);n=O.default.join(n,e.split(".")[0]+"."+this._export.script_suffix);return a.default.existsSync(n)?void 0:(console.log(t,"已被删除"),!0)}})),e=e&&this.genConfigText()}genEnum(){var t=E.CommonUtils.getTemplate(this._export,"ConfigEnum.txt");let i=Object.keys(b.DataModel.Instance.enum);i=i.filter(e=>{return 0<=(null==(e=b.DataModel.Instance.remark[e].generate)?void 0:e.indexOf(this._export.id))});for(let e=0;e<i.length;e++){var o=i[e];let n=b.DataModel.Instance.enum[o],a=new A.CodeWriter;n.forEach((e,t)=>{e.annotation&&a.addStr(E.CommonUtils.getCommentStr(this._codeLang,e.annotation,2)+"\n");t=t==n.length-1;a.add(2,e.key+" = "+e.value+(t?"":","),!t)});var r=B.StrUtils.format(t,o,a.content);q.IOUtils.writeTextFile(O.default.join(this._export.export_com_url,o+"."+this._export.script_suffix),r,j.LineBreak.CRLF,"导出枚举类脚本成功！-> {0}")}return!0}genItemAndVertical(){var d=E.CommonUtils.getTemplate(this._export,"ConfigItem.txt"),i=E.CommonUtils.getTemplate(this._export,"ConfigSingle.txt");for(let e=0;e<this._configNames.length;e++){var g=this._configNames[e],o=b.DataModel.Instance.originConfig[g],c=b.DataModel.Instance.remark[g],f=c.parent&&b.DataModel.Instance.remark[c.parent],m=b.DataModel.Instance.getParents(g);let n;f&&(n=B.StrUtils.convertToLowerCamelCase(m[m.length-1]));m=f&&c.parent+b.DataModel.Instance.config.export_item_suffix;if(o.fixed_keys){var u=b.DataModel.Instance.getConfigUniqueKeyType(g,this._codeLang),p=g+b.DataModel.Instance.config.export_item_suffix;let e="",a=new A.CodeWriter,i="",o="",r=new A.CodeWriter,l=(f&&(e=" : "+m),[["uniqueKey",u,n]]);if(!c.isSingleMainKey){var _=c.mainKeyNames;for(let e=0;e<_.length;e++){var h=_[e],h=b.DataModel.Instance.getConfigKeyType(g,h,this._codeLang),y=b.DataModel.Instance.getMainKeyVarName(e+1);l.push([y,h,n])}for(let e=0;e<_.length;e++){var x=_[e],C=b.DataModel.Instance.getConfigKeyType(g,x,this._codeLang);l.push([x,C,n])}}let t=b.DataModel.Instance.getConfigFixedKeys(g,this._codeLang,!0),s=(t=t.filter(t=>!l.find(e=>e[0]==t[0])),l=[...l,...t],b.DataModel.Instance.getConfigFixedKeys(g,this._codeLang));if(!f&&(a.addStr(E.CommonUtils.getCommentStr(this._codeLang,"唯一主键",2)+"\n"),a.add(2,`public ${u} UniqueKey { set; get; }`),r.add(3,"UniqueKey = uniqueKey;"),!c.isSingleMainKey)){var $=c.mainKeyNames;for(let e=0;e<$.length;e++){var I=$[e],I=b.DataModel.Instance.getConfigKeyType(g,I,this._codeLang),v=(a.addStr(E.CommonUtils.getCommentStr(this._codeLang,`第${e+1}主键`,2)+"\n"),b.DataModel.Instance.getMainKeyVarName(e+1)),M=B.StrUtils.convertToUpperCamelCase(v);a.add(2,`public ${I} ${M} { set; get; }`),r.add(3,M+` = ${v};`)}}for(let n=0;n<l.length;n++){var U=l[n];let t=U[0],e=U[1];var U=U[2],U=b.DataModel.Instance.remark[U]||c,D=null!=s.find(e=>e[0]==t),S=b.DataModel.Instance.isMainKey(g,t),L=B.StrUtils.convertToLowerCamelCase(t),T=B.StrUtils.convertToUpperCamelCase(t),w=D&&(!f||!S),U=(null==U?void 0:U.fields)&&U.fields[t];if(w&&null!=U&&U.annotation&&a.addStr(E.CommonUtils.getCommentStr(this._codeLang,U.annotation,2)+"\n"),null!=U&&U.enum){if(e!=P.CSTypeEnum.Int)return console.log(F.default.red("枚举的值不是整数！-> "+g+" -> "+t)),!1;e=U.enum,w&&a.add(2,`public ${e} ${T} { private set; get; }`,!1),i+=e+" "+L}else if(null!=U&&U.link){U.link;var K=U.link+b.DataModel.Instance.config.export_item_suffix;if(U.linkIsArray){if(e!=P.CSTypeEnum.IntList&&e!=P.CSTypeEnum.StringList)return console.log(F.default.red("链接的值不是整数数组或字符串数组！-> "+g+" -> "+t)),!1;w&&a.add(2,`public IReadOnlyList<${K}> ${T} { private set; get; }`,!1),i+=`IReadOnlyList<${K}> `+L}else w&&a.add(2,`public ${K} ${T} { private set; get; }`,!1),i+=K+" "+L}else w&&a.add(2,`public ${e} ${T} { private set; get; }`,!1),i+=e+" "+L;w&&r.add(3,T+` = ${L};`,!1),!f||D&&!S||(o+=(o?", ":"")+L),n<l.length-1&&(w&&(a.newLine(),r.newLine()),i+=", ")}o=o&&` : base(${o})`;m=B.StrUtils.format(d,p,e,a.content,p,i,o,r.content);q.IOUtils.writeTextFile(O.default.join(this._export.export_com_url,g+b.DataModel.Instance.config.export_item_suffix+"."+this._export.script_suffix),m,j.LineBreak.CRLF,`导出配置Item(${c.sheetType})脚本成功！-> {0}`)}else{let e=new A.CodeWriter,t="",n=new A.CodeWriter;var r=Object.keys(o).length;let a=0;for(const N in o){var l=o[N],l=b.DataModel.Instance.getValueType(l,this._codeLang,!1,g,N),s=c.fields&&c.fields[N],s=(null!=s&&s.annotation&&e.addStr(E.CommonUtils.getCommentStr(this._codeLang,s.annotation,2)+"\n"),B.StrUtils.convertToLowerCamelCase(N)),k=B.StrUtils.convertToUpperCamelCase(N);e.add(2,`public ${l} ${k} { set; get; }`,!1),t+=l+" "+s,n.add(3,k+` = ${s};`,!1),a<r-1&&(t+=", ",e.newLine(),n.newLine()),a++}u=B.StrUtils.format(i,g,e.content,g,t,n.content);q.IOUtils.writeTextFile(O.default.join(this._export.export_com_url,g+"."+this._export.script_suffix),u,j.LineBreak.CRLF,`导出配置(${c.sheetType})脚本成功！-> {0}`)}}return!0}genMgr(){var e=E.CommonUtils.getTemplate(this._export,"ConfigManager.txt"),t=E.CommonUtils.getTemplate(this._export,"ConfigComponent.txt");let l=new A.CodeWriter,s=new A.CodeWriter;for(let t=0;t<this._configNames.length;t++){const k=this._configNames[t];let i=b.DataModel.Instance.originConfig[k];var d=k+b.DataModel.Instance.config.export_item_suffix,g=b.DataModel.Instance.remark[k],c=b.DataModel.Instance.getParents(k);let e=c?[...c].reverse():null;var f=c?c.length:0,m=B.StrUtils.convertToLowerCamelCase(k),u=B.StrUtils.convertToUpperCamelCase(k);s.add(3,"// "+k),s.add(3,`section = sections[${t}];`),s.add(3,'lines = Regex.Split(section, "\\r\\n");');let o=i.fixed_keys;if(o){var p,_=b.DataModel.Instance.getConfigUniqueKeyType(k,this._codeLang);let a=1;g.isSingleMainKey||(p=g.mainKeyNames,a=p.length+1),l.add(2,`public BaseConfig<${_}, ${d}> ${k} { set; get; }`,!1);let n,r=(f?(n="dict"+t,p=B.StrUtils.convertToLowerCamelCase(c[c.length-1])+"Data",s.add(3,`var ${n} = ${p};`),s.add(3,`Dictionary<${_}, ${d}> ${n}_self = new Dictionary<${_}, ${d}>();`)):(n=m+"Data",s.add(3,`Dictionary<${_}, ${d}> ${n} = new Dictionary<${_}, ${d}>();`)),s.add(3,`for (int n = 0; n < lines.Length - 1; n += ${o.length+a})`),s.add(3,"{"),f&&e.forEach((e,t)=>{e+=b.DataModel.Instance.config.export_item_suffix;s.add(4,`var parentItem${t+1} = ${n}[${B.StrUtils.format(b.DataModel.Instance.getParseFuncNameByType(b.DataModel.Instance.getConfigUniqueKeyType(k,this._codeLang,!1),this._codeLang),"lines[n]")}] as ${e};`)}),"");if(g.mainKeyOnlyOneAndIsEnum&&(r+=`(${_}) `),r+=B.StrUtils.format(b.DataModel.Instance.getParseFuncNameByType(b.DataModel.Instance.getConfigUniqueKeyType(k,this._codeLang,!1),this._codeLang),"lines[n]"),!g.isSingleMainKey){r+=", ";var h=g.mainKeyNames;for(let e=0;e<h.length;e++){var y=h[e],y=b.DataModel.Instance.getConfigKeyType(k,y,this._codeLang);r+=B.StrUtils.format(b.DataModel.Instance.getParseFuncNameByType(y,this._codeLang),`lines[n + ${e+1}]`),e!=h.length-1&&(r+=", ")}}if(0<o.length&&(r+=", "),f){let o=[];e.forEach((e,t)=>{var n=b.DataModel.Instance.originConfig[e].fixed_keys;for(let e=0;e<n.length;e++){var a,i=n[e];-1==o.indexOf(i)&&(a=B.StrUtils.convertToUpperCamelCase(i),r+=`parentItem${t+1}.${a}, `,o.push(i))}})}for(let t=0;t<o.length;t++){var x=o[t];let n=!1;if(f)for(let t=c.length-1;!n&&0<=t;t--){var C=c[t];let e=b.DataModel.Instance.originConfig[C].fixed_keys;-1!=e.indexOf(x)&&(n=!0)}if(!n){let e=b.DataModel.Instance.getConfigKeyType(k,x,this._codeLang);var $=(null==g?void 0:g.fields)&&g.fields[x];if(null!=$&&$.enum){if(e!=P.CSTypeEnum.Int)return console.log(F.default.red("枚举的值不是整数！-> "+k+" -> "+x)),!1;e=$.enum,r=(r+=`(${e}) `)+B.StrUtils.format(b.DataModel.Instance.getParseFuncNameByType(P.CSTypeEnum.Int,this._codeLang),`lines[n + ${t+a}]`)}else if(null!=$&&$.link){var I=$.link;if($.linkIsArray){if(e!=P.CSTypeEnum.IntList&&e!=P.CSTypeEnum.StringList)return console.log(F.default.red("链接的值不是整数数组或字符串数组！-> "+k+" -> "+x)),!1;var v=b.DataModel.Instance.getConfigUniqueKeyType($.link,this._codeLang),M=$.link+b.DataModel.Instance.config.export_item_suffix;r+=`ConfigUtility.GetLinkedConfigs<${v}, ${M}>(${B.StrUtils.format(b.DataModel.Instance.getParseFuncNameByType(e,this._codeLang),`lines[n + ${t+a}]`)}, ${$.link})`}else r+=`self.${I}.Get(${B.StrUtils.format(b.DataModel.Instance.getParseFuncNameByType(P.CSTypeEnum.Int,this._codeLang),`lines[n + ${t+a}]`)})`}else r+=B.StrUtils.format(b.DataModel.Instance.getParseFuncNameByType(e,this._codeLang),`lines[n + ${t+a}]`);t<o.length-1&&(r+=", ")}}if(2<=(null===r||void 0===r?void 0:r.length)&&", "==r.substring(r.length-2,r.length)&&(r=r.substring(0,r.length-2)),s.add(4,`var item = new ${d}(${r});`),f?(s.add(4,n+"[item.UniqueKey] = item;"),s.add(4,n+"_self[item.UniqueKey] = item;")):s.add(4,n+"[item.UniqueKey] = item;"),s.add(3,"}"),f?s.add(3,`self.${k} = new BaseConfig<${_}, ${d}>("${k}", ${n}_self);`,!1):s.add(3,`self.${k} = new BaseConfig<${_}, ${d}>("${k}", ${n});`,!1),!g.isSingleMainKey){var U=B.StrUtils.convertToUpperCamelCase(b.DataModel.MainKeyVarName),D=(l.newLine(),g.mainKeyNames),m=b.DataModel.Instance.getConfigCollectionTypeByIndex(k,this._codeLang),S=""+u+b.DataModel.Instance.config.export_collection_suffix;l.add(2,`public ${m} ${S} { set; get; }`),s.newLine(),s.add(3,`self.${S} = new ${m}();`),s.add(3,`foreach (var keyValuePair in self.${u}.Data)`),s.add(3,"{"),s.add(4,"var item = keyValuePair.Value;");for(let n=1;n<D.length;n++){s.add(4,"if (!self."+S,!1);let t=n;for(let e=0;e<t;e++){var L=e+1;e!=t-1?s.add(0,`[item.${U}${L}]`,!1):s.add(0,`.ContainsKey(item.${U}${L})`,!1)}s.add(0,")"),s.add(5,"self."+S,!1),t=n;for(let e=0;e<t;e++){var T,w=e+1;e!=t-1?s.add(0,`[item.${U}${w}]`,!1):(T=b.DataModel.Instance.getConfigCollectionTypeByIndex(k,this._codeLang,n,D.length-1),s.add(0,`[item.${U}${w}] = new ${T}();`))}}s.add(4,"self."+S,!1);for(let e=0;e<D.length;e++){var K=e+1;e!=D.length-1?s.add(0,`[item.${U}${K}]`,!1):s.add(0,`[item.${U}${K}] = item;`)}s.add(3,"}")}}else{l.add(2,`public ${k} ${k} { set; get; }`,!1),o=Object.keys(i);let a="";o.forEach((e,t)=>{var n=i[e],n=b.DataModel.Instance.getValueType(n,this._codeLang,!1,k,e);a+=B.StrUtils.format(b.DataModel.Instance.getParseFuncNameByType(n,this._codeLang),`lines[${t}]`),t<o.length-1&&(a+=", ")}),s.add(3,`self.${k} = new ${k}("${k}", ${a});`,!1)}t<this._configNames.length-1&&(l.newLine(),s.newLine(2))}e=B.StrUtils.format(e,s.content),t=B.StrUtils.format(t,l.content);return q.IOUtils.writeTextFile(O.default.join(this._export.export_script_url,this._export.export_config_manager_name+"."+this._export.script_suffix),e,j.LineBreak.CRLF,`成功导出配置管理类脚本（${this._export.export_config_manager_name}）-> {0}`),q.IOUtils.writeTextFile(O.default.join(this._export.export_com_url,this._export.export_config_com_name+"."+this._export.script_suffix),t,j.LineBreak.CRLF,`成功导出配置组件脚本（${this._export.export_config_manager_name}）-> {0}`),!0}genConfigText(){let n=new A.CodeWriter;for(let e=0;e<this._configNames.length;e++){var a=this._configNames[e],t=b.DataModel.Instance.originConfig[a],i=t.fixed_keys;if(0<e&&n.add(0,"#",!1),i){var o=t.data;for(const s in o){var r=o[s];let e=isNaN(+s)?s:+s,t=(n.add(0,e),isNaN(+s)&&e.split("_"));var l=b.DataModel.Instance.remark[a];t&&1<t.length&&null!=l&&l.mainKeySubs&&t.length==l.mainKeySubs.length&&t.forEach(e=>{e=isNaN(+e)?e:+e,n.add(0,e)});for(let t=0;t<r.length;t++){let e=r[t];e=Array.isArray(e)?JSON.stringify(e):(null===e||void 0===e?void 0:e.toString())||"",n.add(0,""+e)}}}else for(const d in t){let e=t[d];e=Array.isArray(e)?JSON.stringify(e):null!=e&&(null===e||void 0===e?void 0:e.toString())||"",n.add(0,""+e)}}var e=c.default.parse(n.content),e=g.default.stringify(e);return q.IOUtils.writeTextFile(this._export.export_url,e,null,"导出配置文本成功！-> {0}"),!0}}exports.GenETCSModule=t;