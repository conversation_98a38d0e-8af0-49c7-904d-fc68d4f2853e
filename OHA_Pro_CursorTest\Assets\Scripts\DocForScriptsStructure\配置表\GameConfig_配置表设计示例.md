# GameConfig配置表设计示例

本文档提供具体的Excel配置表设计示例，展示如何为现有游戏系统设计配置表。

## 1. 技能系统配置表示例

### 1.1 SkillConfig.xlsx - 技能基础配置

```
行1: SkillConfig    Horizontal
行2: skillId#string skillName#string    skillType#enum      category#enum       framesDuration#int  postMoveDuration#int    cooldownTime#float  energyCost#int  baseDamage#float    animationClip#string    timelineAsset#string    description#string  icon#string
行3: string         string              enum#SkillType      enum#SkillCategory  int                 int                     float               int             float               string                  string                  string              string
行4: 技能ID         技能名称            技能类型            技能类别            持续帧数            后摇帧数                冷却时间            能量消耗        基础伤害            动画片段                Timeline资源            技能描述            图标路径
行5: 1              1                   1                   1                   1                   1                       1                   1               1                   1                       1                       1                   1
行6: CS             C                   CS                  CS                  CS                  CS                      CS                  CS              CS                  CS                      CS                      CS                  CS
行7: skill_fireball 火球术              Magic               Attack              60                  30                      5.0                 20              100.0               Skill_Fireball          Timeline/Fireball       发射一个火球攻击敌人    Icons/skill_fireball
行8: skill_heal     治疗术              Magic               Support             30                  15                      8.0                 30              0                   Skill_Heal              Timeline/Heal           治疗目标单位            Icons/skill_heal
行9: skill_charge   冲锋                Physical            Attack              45                  20                      6.0                 15              80.0                Skill_Charge            Timeline/Charge         向前冲锋攻击敌人        Icons/skill_charge
行10: skill_shield  护盾术              Magic               Defense             20                  10                      12.0                25              0                   Skill_Shield            Timeline/Shield         为自己施加护盾          Icons/skill_shield
```

### 1.2 SkillAttackConfig.xlsx - 技能攻击配置

```
行1: SkillAttackConfig  Horizontal
行2: skillId#string     attackIndex#int damageMultiplier#float hitStunDuration#float   canBreakArmor#bool  knockbackForce#float    poiseDamage#float   isCritical#bool targetRelationship#enum    attackBoxSizeType#string    triggerInterval#float   hitCount#int    maxHitsPerTarget#int    createFrame#int destroyFrame#int    offset#string
行3: string             int             float                   float                   bool                float                   float               bool            enum#UnitRelationship      string                      float                   int             int                     int             int                 string
行4: 技能ID             攻击序号        伤害倍率                硬直时间                是否可破防          击退力度                削韧值              是否必定暴击    目标关系                    攻击盒类型                  触发间隔                最大触发次数    单目标最大命中次数      创建帧          销毁帧              偏移量
行5: 1                  1               1                       1                       1                   1                       1                   1               1                           1                           1                       1               1                       1               1                   1
行6: CS                 CS              CS                      CS                      CS                  CS                      CS                  CS              CS                          CS                          CS                      CS              CS                      CS              CS                  CS
行7: skill_fireball     1               1.5                     0.5                     false               100.0                   10.0                false           Enemy                       Normal                      0.1                     1               1                       15              45              0,0,1
行8: skill_heal         1               0                       0                       false               0                       0                   false           Ally                        Normal                      0                       1               1                       10              25              0,0,0
行9: skill_charge       1               1.2                     0.8                     true                150.0                   15.0                false           Enemy                       Large                       0.05                    3               1                       20              40              0,0,0.5
行10: skill_shield      1               0                       0                       false               0                       0                   false           Self                        Normal                      0                       1               1                       5               15              0,0,0
```

### 1.3 SkillLogicConfig.xlsx - 技能逻辑配置

```
行1: SkillLogicConfig   Horizontal
行2: skillId#string     triggerFrame#int    destroyFrame#int    triggerAPI#string                                   condition#string                actionConfig#string
行3: string             int                 int                 string                                              string                          string
行4: 技能ID             触发帧数            销毁帧数            触发器API配置                                       触发条件                        动作配置
行5: 1                  1                   1                   1                                                   1                               1
行6: CS                 CS                  CS                  CS                                                  CS                              CS
行7: skill_fireball     15                  30                  DamageTrigger:threshold=100,target=enemy            GetHealth_API(target) < 0.5     AddBuff:buffId=buff_fire,target=hitTarget;PlayFX:fxName=explosion,duration=2.0
行8: skill_fireball     25                  0                                                                       GetHealth_API(target) >= 0.5    DealDamage:damage=100,target=hitTarget;PlayFX:fxName=fireball_hit
行9: skill_heal         10                  0                                                                       GetMana_API(caster) >= 30       Heal:amount=100,target=target;RemoveBuff:buffId=buff_poison,target=target
行10: skill_charge      20                  35                  DistanceTrigger:distance=3.0,target=enemy          layer > 0                       DealDamage:damage=120,target=hitTarget;AddBuff:buffId=buff_stun,target=hitTarget
行11: skill_shield      5                   0                                                                       GetAttribute_API(self,Health_Attribute) < 200  AddBuff:buffId=buff_shield,target=self;PlayFX:fxName=shield_cast
```

### 1.4 SkillTypeEnum.xlsx - 技能类型枚举

```
行1: SkillType      Enum
行2: Physical       1       物理技能
行3: Magic          2       魔法技能
行4: Hybrid         3       混合技能
```

### 1.5 SkillCategoryEnum.xlsx - 技能类别枚举

```
行1: SkillCategory  Enum
行2: Attack         1       攻击技能
行3: Defense        2       防御技能
行4: Support        3       辅助技能
行5: Movement       4       移动技能
```

## 2. Buff系统配置表示例

### 2.1 BuffConfig.xlsx - Buff基础配置

```
行1: BuffConfig         Horizontal
行2: buffId#string      buffName#string buffTag#enum        duration#float  isPermanent#bool    mutilAddType#enum           removeOneLayerOnTimeUp#bool iconPath#string         effectValue#float   tickInterval#float
行3: string             string          enum#BuffTag        float           bool                enum#BuffMutilAddType      bool                        string                  float               float
行4: BuffID             Buff名称        Buff标签            持续时间        是否永久            重复添加方式                时间到时是否只移除一层      图标路径                效果数值            周期间隔
行5: 1                  1               1                   1               1                   1                           1                           1                       1                   1
行6: CS                 C               CS                  CS              CS                  CS                          CS                          CS                      CS                  CS
行7: buff_fire          燃烧效果        Damage              10.0            false               multipleLayer               true                        Icons/Buffs/Fire        5.0                 1.0
行8: buff_poison        中毒效果        Damage              15.0            false               resetTime                   false                       Icons/Buffs/Poison      3.0                 2.0
行9: buff_shield        护盾效果        Defense             30.0            false               replace                     false                       Icons/Buffs/Shield      100.0               0
行10: buff_speed        加速效果        Buff                8.0             false               multipleLayer               true                        Icons/Buffs/Speed       1.5                 0
行11: buff_stun         眩晕效果        Debuff              3.0             false               resetTime                   false                       Icons/Buffs/Stun        0                   0
```

### 2.2 BuffLogicConfig.xlsx - Buff逻辑配置

```
行1: BuffLogicConfig    Horizontal
行2: buffId#string      logicType#enum          actionConfig#string                                                     triggerAPI#string                           tickInterval#float  conditions#string
行3: string             enum#BuffLogicType      string                                                                  string                                      float               string
行4: BuffID             逻辑类型                动作配置                                                                触发器API配置                               周期间隔            执行条件
行5: 1                  1                       1                                                                       1                                           1                   1
行6: CS                 CS                      CS                                                                      CS                                          CS                  CS
行7: buff_fire          OnStart                 PlayFX:fxName=fire_start,target=self                                                                               0                   
行8: buff_fire          OnTick                  AttributeModify:attribute=Health_Attribute,value=-5,target=self;PlayFX:fxName=fire_tick,target=self              1.0                 layer > 0
行9: buff_fire          OnEnd                   PlayFX:fxName=fire_end,target=self                                                                                 0                   
行10: buff_poison       OnTick                  AttributeModify:attribute=Health_Attribute,value=-3,target=self                                                    2.0                 layer > 0
行11: buff_shield       OnStart                 AttributeModify:attribute=Def_Attribute,value=50,target=self;PlayFX:fxName=shield_activate,target=self           0                   
行12: buff_shield       OnRemove                AttributeModify:attribute=Def_Attribute,value=-50,target=self                                                      0                   
行13: buff_speed        OnStart                 AttributeModify:attribute=MoveSpeed_Attribute,value=2,target=self                                                  0                   
行14: buff_speed        OnRemove                AttributeModify:attribute=MoveSpeed_Attribute,value=-2,target=self                                                 0                   
行15: buff_stun         OnStart                 AttributeModify:attribute=MoveSpeed_Attribute,value=-999,target=self;PlayFX:fxName=stun_effect,target=self       0                   
行16: buff_stun         OnRemove                AttributeModify:attribute=MoveSpeed_Attribute,value=999,target=self                                                0                   
```

### 2.3 BuffTagEnum.xlsx - Buff标签枚举

```
行1: BuffTag    Enum
行2: Buff       1       增益效果
行3: Debuff     2       减益效果
行4: Damage     3       伤害效果
行5: Defense    4       防御效果
```

### 2.4 BuffLogicTypeEnum.xlsx - Buff逻辑类型枚举

```
行1: BuffLogicType  Enum
行2: OnStart        1       Buff开始时执行
行3: OnEnd          2       Buff结束时执行
行4: OnTick         3       周期性执行
行5: OnLayerChange  4       层数变化时执行
行6: OnRemove       5       Buff被移除时执行
```

### 2.5 BuffMutilAddTypeEnum.xlsx - Buff重复添加类型枚举

```
行1: BuffMutilAddType   Enum
行2: replace            1       替换现有Buff
行3: multipleLayer      2       增加层数
行4: resetTime          3       重置时间
行5: ignore             4       忽略新Buff
```

## 3. 单位系统配置表示例

### 3.1 CharacterConfig.xlsx - 角色配置

```
行1: CharacterConfig    Horizontal
行2: characterId#string characterName#string    prefabPath#string           baseHealth#float    baseAttack#float    baseDefense#float   moveSpeed#float faction#enum        skillIds#string[]                           initialBuffs#string[]
行3: string             string                  string                      float               float               float               float           enum#UnitFaction   string[]                                    string[]
行4: 角色ID             角色名称                预制体路径                  基础生命值          基础攻击力          基础防御力          移动速度        阵营                技能ID列表                                  初始Buff列表
行5: 1                  1                       1                           1                   1                   1                   1               1                   1                                           1
行6: CS                 C                       CS                          CS                  CS                  CS                  CS              CS                  CS                                          CS
行7: player_warrior     战士                    Units/Player/Warrior        1000.0              100.0               50.0                5.0             Player              skill_fireball|skill_charge|skill_shield   buff_health_regen
行8: player_mage        法师                    Units/Player/Mage           800.0               120.0               30.0                4.5             Player              skill_fireball|skill_heal                   buff_mana_regen
行9: player_archer      弓箭手                  Units/Player/Archer         900.0               110.0               40.0                5.5             Player              skill_arrow_shot|skill_multi_shot           buff_speed
```

### 3.2 MonsterConfig.xlsx - 怪物配置

```
行1: MonsterConfig      Horizontal
行2: monsterId#string   monsterName#string  prefabPath#string               baseHealth#float    baseAttack#float    baseDefense#float   moveSpeed#float faction#enum    skillIds#string[]               initialBuffs#string[]   aiType#enum     expReward#int   dropItems#string[]
行3: string             string              string                          float               float               float               float           enum#UnitFaction   string[]                        string[]                enum#AIType     int             string[]
行4: 怪物ID             怪物名称            预制体路径                      基础生命值          基础攻击力          基础防御力          移动速度        阵营                技能ID列表                      初始Buff列表            AI类型          经验奖励        掉落物品
行5: 1                  1                   1                               1                   1                   1                   1               1                   1                               1                       1               1               1
行6: CS                 C                   CS                              CS                  CS                  CS                  CS              CS                  CS                              CS                      CS              CS              CS
行7: goblin_warrior     哥布林战士          Units/Monsters/Goblin          500.0               80.0                30.0                3.0             Enemy               skill_slash|skill_roar          buff_rage               Aggressive      100             item_coin|item_potion
行8: orc_shaman         兽人萨满            Units/Monsters/OrcShaman       600.0               90.0                25.0                2.5             Enemy               skill_heal|skill_lightning      buff_mana_regen         Defensive       150             item_coin|item_mana_potion
行9: skeleton_archer    骷髅弓箭手          Units/Monsters/SkeletonArcher  400.0               100.0               20.0                4.0             Enemy               skill_arrow_shot                                        Ranged          80              item_coin|item_arrow
```

### 3.3 UnitFactionEnum.xlsx - 单位阵营枚举

```
行1: UnitFaction    Enum
行2: Player         1       玩家阵营
行3: Enemy          2       敌方阵营
行4: Neutral        3       中立阵营
行5: NPC            4       NPC阵营
```

### 3.4 AITypeEnum.xlsx - AI类型枚举

```
行1: AIType         Enum
行2: Aggressive     1       主动攻击型
行3: Defensive      2       防御型
行4: Passive        3       被动型
行5: Ranged         4       远程攻击型
行6: Support        5       辅助型
```

## 4. 逻辑配置高级示例

### 4.1 复杂技能逻辑配置示例

```
# 技能：连击火球术
# 逻辑：释放3个火球，每个火球间隔0.5秒，如果目标生命值低于30%则造成双倍伤害

skillId: skill_combo_fireball
triggerFrame: 15, condition: "", actionConfig: "CreateProjectile:projectileId=fireball_1,target=target,delay=0"
triggerFrame: 45, condition: "", actionConfig: "CreateProjectile:projectileId=fireball_2,target=target,delay=0"
triggerFrame: 75, condition: "", actionConfig: "CreateProjectile:projectileId=fireball_3,target=target,delay=0"
triggerFrame: 15, condition: "GetHealth_API(target) < 0.3", actionConfig: "ModifyProjectileDamage:projectileId=fireball_1,multiplier=2.0"
triggerFrame: 45, condition: "GetHealth_API(target) < 0.3", actionConfig: "ModifyProjectileDamage:projectileId=fireball_2,multiplier=2.0"
triggerFrame: 75, condition: "GetHealth_API(target) < 0.3", actionConfig: "ModifyProjectileDamage:projectileId=fireball_3,multiplier=2.0"
```

### 4.2 复杂Buff逻辑配置示例

```
# Buff：狂暴状态
# 逻辑：每秒增加攻击力，最多叠加10层，生命值低于50%时额外增加暴击率

buffId: buff_berserk
logicType: OnStart, actionConfig: "AttributeModify:attribute=Atk_Attribute,value=10,target=self;PlayFX:fxName=berserk_start"
logicType: OnTick, tickInterval: 1.0, conditions: "layer < 10", actionConfig: "AttributeModify:attribute=Atk_Attribute,value=5,target=self"
logicType: OnTick, tickInterval: 0.5, conditions: "GetHealth_API(self) < 0.5", actionConfig: "AttributeModify:attribute=CritRate_Attribute,value=5,target=self"
logicType: OnLayerChange, conditions: "layer >= 10", actionConfig: "PlayFX:fxName=berserk_max;AddBuff:buffId=buff_unstoppable,target=self"
logicType: OnRemove, actionConfig: "AttributeModify:attribute=Atk_Attribute,value=-50,target=self;AttributeModify:attribute=CritRate_Attribute,value=-25,target=self"
```

### 4.3 触发器配置示例

```
# 距离触发器：敌人接近时自动释放技能
triggerAPI: "DistanceTrigger:distance=5.0,target=enemy,action=CastSkill:skillId=skill_auto_defense"

# 时间触发器：持续伤害效果
triggerAPI: "TimeTrigger:delay=1.0,interval=2.0,maxTriggers=5,action=DealDamage:damage=20,target=self"

# 生命值触发器：生命值低于阈值时触发
triggerAPI: "HealthTrigger:threshold=0.3,action=AddBuff:buffId=buff_last_stand,target=self"

# 组合触发器：多个条件同时满足
triggerAPI: "ComboTrigger:triggers=DistanceTrigger:distance=3.0|HealthTrigger:threshold=0.5,action=CastSkill:skillId=skill_emergency_escape"
```

## 5. 配置表设计最佳实践

### 5.1 命名规范

1. **表名**：使用PascalCase，如`SkillConfig`、`BuffLogicConfig`
2. **字段名**：使用camelCase，如`skillId`、`actionConfig`
3. **枚举值**：使用PascalCase，如`Physical`、`OnStart`
4. **ID规范**：使用有意义的字符串ID，如`skill_fireball`、`buff_fire_damage`

### 5.2 数据组织原则

1. **单一职责**：每个表只负责一类数据
2. **逻辑分离**：基础配置与逻辑配置分离
3. **引用完整性**：确保所有引用的ID都存在
4. **版本兼容**：考虑配置的向后兼容性

### 5.3 性能考虑

1. **索引优化**：合理设置主键和索引
2. **数据大小**：避免过大的配置项
3. **加载策略**：考虑按需加载和预加载
4. **缓存策略**：合理使用配置缓存

### 5.4 维护性设计

1. **注释完整**：为每个字段提供清晰的注释
2. **示例丰富**：提供典型的配置示例
3. **验证规则**：定义数据验证规则
4. **文档同步**：保持配置文档与实际配置同步

通过这些详细的配置表设计示例，团队可以快速理解如何为现有游戏系统设计配置表，实现数据驱动的游戏开发模式。 