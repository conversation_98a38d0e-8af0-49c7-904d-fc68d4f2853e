"use strict";var e=this&&this.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(exports,"__esModule",{value:!0}),exports.GenCSModule=void 0;const q=e(require("cli-color")),F=e(require("path")),O=require("../utils/IOUtils"),b=require("../utils/StrUtils"),B=require("../DataModel"),a=e(require("fs")),E=require("../utils/CommonUtils"),P=require("../CSTypeEnum"),j=require("../utils/LineBreak"),A=require("../utils/CodeWriter"),g=e(require("crypto-js/enc-base64")),c=e(require("crypto-js/enc-utf8"));class t{static get Instance(){return null==this._instance&&(this._instance=new t),this._instance}gen(t){this._export=B.DataModel.Instance.config.exports.find(e=>e.id==t),this._codeLang=this._export.code_language,console.log(`
================================= 开始生成 ${this._export.id} 配置 =================================
`),O.IOUtils.deleteFolderFileByCondition(this._export.export_script_url,e=>{if(".meta"!=F.default.extname(e))return!0}),O.IOUtils.makeDir(this._export.export_script_url),O.IOUtils.copy(`templates/${this._export.template_name||this._export.id}/scripts/`,this._export.export_script_url),this._configNames=B.DataModel.Instance.getConfigNamesAndCutDataByConfigType(this._export.id),this._configSplitor=B.DataModel.Instance.config.export_data_splitor,B.DataModel.Instance.config.export_data_splitor_random_enabled&&(this._configSplitor=b.StrUtils.genPassword(8,!0,!0,!1,!0));let e=this.genEnum();return(e=(e=e&&this.genItemAndVertical())&&this.genMgr())&&O.IOUtils.deleteFolderFileByCondition(this._export.export_script_url,t=>{if(".meta"==F.default.extname(t)){var n=F.default.dirname(t);let e=F.default.basename(t);n=F.default.join(n,e.split(".")[0]+"."+this._export.script_suffix);return a.default.existsSync(n)?void 0:(console.log(t,"已被删除"),!0)}}),e=e&&this.genConfigText()}genEnum(){var t=E.CommonUtils.getTemplate(this._export,"ConfigEnum.txt");let i=Object.keys(B.DataModel.Instance.enum);i=i.filter(e=>{return 0<=(null==(e=B.DataModel.Instance.remark[e].generate)?void 0:e.indexOf(this._export.id))});for(let e=0;e<i.length;e++){var r=i[e];let n=B.DataModel.Instance.enum[r],a=new A.CodeWriter;n.forEach((e,t)=>{e.annotation&&a.addStr(E.CommonUtils.getCommentStr(this._codeLang,e.annotation,2)+"\n");t=t==n.length-1;a.add(2,e.key+" = "+e.value+(t?"":","),!t)});var o=b.StrUtils.format(t,r,a.content);O.IOUtils.writeTextFile(F.default.join(this._export.export_script_url,r+"."+this._export.script_suffix),o,j.LineBreak.CRLF,"导出枚举类脚本成功！-> {0}")}return!0}genItemAndVertical(){var d=E.CommonUtils.getTemplate(this._export,"ConfigItem.txt"),i=E.CommonUtils.getTemplate(this._export,"ConfigSingle.txt");for(let e=0;e<this._configNames.length;e++){var g=this._configNames[e],r=B.DataModel.Instance.originConfig[g],c=B.DataModel.Instance.remark[g],f=c.parent&&B.DataModel.Instance.remark[c.parent],u=B.DataModel.Instance.getParents(g);let n;f&&(n=b.StrUtils.convertToLowerCamelCase(u[u.length-1]));u=f&&c.parent+B.DataModel.Instance.config.export_item_suffix;if(r.fixed_keys){var p=B.DataModel.Instance.getConfigUniqueKeyType(g,this._codeLang),m=g+B.DataModel.Instance.config.export_item_suffix;let e="",a=new A.CodeWriter,i="",r="",o=new A.CodeWriter,s=(f&&(e=" : "+u),[["uniqueKey",p,n]]);if(!c.isSingleMainKey){var _=c.mainKeyNames;for(let e=0;e<_.length;e++){var h=_[e],h=B.DataModel.Instance.getConfigKeyType(g,h,this._codeLang),y=B.DataModel.Instance.getMainKeyVarName(e+1);s.push([y,h,n])}for(let e=0;e<_.length;e++){var C=_[e],$=B.DataModel.Instance.getConfigKeyType(g,C,this._codeLang);s.push([C,$,n])}}let t=B.DataModel.Instance.getConfigFixedKeys(g,this._codeLang,!0),l=(t=t.filter(t=>!s.find(e=>e[0]==t[0])),s=[...s,...t],B.DataModel.Instance.getConfigFixedKeys(g,this._codeLang));if(!f&&(a.addStr(E.CommonUtils.getCommentStr(this._codeLang,"唯一主键",2)+"\n"),a.add(2,`public ${p} UniqueKey { private set; get; }`),o.add(3,"UniqueKey = uniqueKey;"),!c.isSingleMainKey)){var x=c.mainKeyNames;for(let e=0;e<x.length;e++){var I=x[e],I=B.DataModel.Instance.getConfigKeyType(g,I,this._codeLang),v=(a.addStr(E.CommonUtils.getCommentStr(this._codeLang,`第${e+1}主键`,2)+"\n"),B.DataModel.Instance.getMainKeyVarName(e+1)),M=b.StrUtils.convertToUpperCamelCase(v);a.add(2,`public ${I} ${M} { private set; get; }`),o.add(3,M+` = ${v};`)}}for(let n=0;n<s.length;n++){var D=s[n];let t=D[0],e=D[1];var D=D[2],D=B.DataModel.Instance.remark[D]||c,S=null!=l.find(e=>e[0]==t),U=B.DataModel.Instance.isMainKey(g,t),L=b.StrUtils.convertToLowerCamelCase(t),T=b.StrUtils.convertToUpperCamelCase(t),K=S&&(!f||!U),D=(null==D?void 0:D.fields)&&D.fields[t];if(K&&null!=D&&D.annotation&&a.addStr(E.CommonUtils.getCommentStr(this._codeLang,D.annotation,2)+"\n"),null!=D&&D.enum){if(e!=P.CSTypeEnum.Int)return console.log(q.default.red("枚举的值不是整数！-> "+g+" -> "+t)),!1;e=D.enum,K&&a.add(2,`public ${e} ${T} { private set; get; }`,!1),i+=e+" "+L}else if(null!=D&&D.link){D.link;var w=D.link+B.DataModel.Instance.config.export_item_suffix;if(D.linkIsArray){if(e!=P.CSTypeEnum.IntList&&e!=P.CSTypeEnum.StringList)return console.log(q.default.red("链接的值不是整数数组或字符串数组！-> "+g+" -> "+t)),!1;K&&a.add(2,`public IReadOnlyList<${w}> ${T} { private set; get; }`,!1),i+=`IReadOnlyList<${w}> `+L}else K&&a.add(2,`public ${w} ${T} { private set; get; }`,!1),i+=w+" "+L}else K&&a.add(2,`public ${e} ${T} { private set; get; }`,!1),i+=e+" "+L;K&&o.add(3,T+` = ${L};`,!1),!f||S&&!U||(r+=(r?", ":"")+L),n<s.length-1&&(K&&(a.newLine(),o.newLine()),i+=", ")}r=r&&` : base(${r})`;u=b.StrUtils.format(d,m,e,a.content,m,i,r,o.content);O.IOUtils.writeTextFile(F.default.join(this._export.export_script_url,g+B.DataModel.Instance.config.export_item_suffix+"."+this._export.script_suffix),u,j.LineBreak.CRLF,`导出配置Item(${c.sheetType})脚本成功！-> {0}`)}else{let e=new A.CodeWriter,t="",n=new A.CodeWriter;var o=Object.keys(r).length;let a=0;for(const N in r){var s=r[N],s=B.DataModel.Instance.getValueType(s,this._codeLang,!1,g,N),l=c.fields&&c.fields[N],l=(null!=l&&l.annotation&&e.addStr(E.CommonUtils.getCommentStr(this._codeLang,l.annotation,2)+"\n"),b.StrUtils.convertToLowerCamelCase(N)),k=b.StrUtils.convertToUpperCamelCase(N);e.add(2,`public ${s} ${k} { private set; get; }`,!1),t+=s+" "+l,n.add(3,k+` = ${l};`,!1),a<o-1&&(t+=", ",e.newLine(),n.newLine()),a++}p=b.StrUtils.format(i,g,e.content,g,t,n.content);O.IOUtils.writeTextFile(F.default.join(this._export.export_script_url,g+"."+this._export.script_suffix),p,j.LineBreak.CRLF,`导出配置(${c.sheetType})脚本成功！-> {0}`)}}return!0}genMgr(){var e=E.CommonUtils.getTemplate(this._export,"ConfigMgr.txt");let s=new A.CodeWriter,l=new A.CodeWriter;for(let t=0;t<this._configNames.length;t++){const k=this._configNames[t];let i=B.DataModel.Instance.originConfig[k];var d=k+B.DataModel.Instance.config.export_item_suffix,g=B.DataModel.Instance.remark[k],c=B.DataModel.Instance.getParents(k);let e=c?[...c].reverse():null;var f=c?c.length:0,u=b.StrUtils.convertToLowerCamelCase(k),p=b.StrUtils.convertToUpperCamelCase(k);l.add(3,"// "+k),l.add(3,`section = sections[${t}];`),l.add(3,'lines = Regex.Split(section, "\\r\\n");');let r=i.fixed_keys;if(r){var m,_=B.DataModel.Instance.getConfigUniqueKeyType(k,this._codeLang);let a=1;g.isSingleMainKey||(m=g.mainKeyNames,a=m.length+1),s.add(2,`public static BaseConfig<${_}, ${d}> ${k} { private set; get; }`,!1);let n,o=(f?(n="dict"+t,m=b.StrUtils.convertToLowerCamelCase(c[c.length-1])+"Data",l.add(3,`var ${n} = ${m};`),l.add(3,`Dictionary<${_}, ${d}> ${n}_self = new Dictionary<${_}, ${d}>();`)):(n=u+"Data",l.add(3,`Dictionary<${_}, ${d}> ${n} = new Dictionary<${_}, ${d}>();`)),l.add(3,`for (int n = 0; n < lines.Length - 1; n += ${r.length+a})`),l.add(3,"{"),f&&e.forEach((e,t)=>{e+=B.DataModel.Instance.config.export_item_suffix;l.add(4,`var parentItem${t+1} = ${n}[${b.StrUtils.format(B.DataModel.Instance.getParseFuncNameByType(B.DataModel.Instance.getConfigUniqueKeyType(k,this._codeLang,!1),this._codeLang),"lines[n]")}] as ${e};`)}),"");if(g.mainKeyOnlyOneAndIsEnum&&(o+=`(${_}) `),o+=b.StrUtils.format(B.DataModel.Instance.getParseFuncNameByType(B.DataModel.Instance.getConfigUniqueKeyType(k,this._codeLang,!1),this._codeLang),"lines[n]"),!g.isSingleMainKey){o+=", ";var h=g.mainKeyNames;for(let e=0;e<h.length;e++){var y=h[e],y=B.DataModel.Instance.getConfigKeyType(k,y,this._codeLang);o+=b.StrUtils.format(B.DataModel.Instance.getParseFuncNameByType(y,this._codeLang),`lines[n + ${e+1}]`),e!=h.length-1&&(o+=", ")}}if(0<r.length&&(o+=", "),f){let r=[];e.forEach((e,t)=>{var n=B.DataModel.Instance.originConfig[e].fixed_keys;for(let e=0;e<n.length;e++){var a,i=n[e];-1==r.indexOf(i)&&(a=b.StrUtils.convertToUpperCamelCase(i),o+=`parentItem${t+1}.${a}, `,r.push(i))}})}for(let t=0;t<r.length;t++){var C=r[t];let n=!1;if(f)for(let t=c.length-1;!n&&0<=t;t--){var $=c[t];let e=B.DataModel.Instance.originConfig[$].fixed_keys;-1!=e.indexOf(C)&&(n=!0)}if(!n){let e=B.DataModel.Instance.getConfigKeyType(k,C,this._codeLang);var x=(null==g?void 0:g.fields)&&g.fields[C];if(null!=x&&x.enum){if(e!=P.CSTypeEnum.Int)return console.log(q.default.red("枚举的值不是整数！-> "+k+" -> "+C)),!1;e=x.enum,o=(o+=`(${e}) `)+b.StrUtils.format(B.DataModel.Instance.getParseFuncNameByType(P.CSTypeEnum.Int,this._codeLang),`lines[n + ${t+a}]`)}else if(null!=x&&x.link){var I=x.link;if(x.linkIsArray){if(e!=P.CSTypeEnum.IntList&&e!=P.CSTypeEnum.StringList)return console.log(q.default.red("链接的值不是整数数组或字符串数组！-> "+k+" -> "+C)),!1;var v=B.DataModel.Instance.getConfigUniqueKeyType(x.link,this._codeLang),M=x.link+B.DataModel.Instance.config.export_item_suffix;o+=`ConfigUtility.GetLinkedConfigs<${v}, ${M}>(${b.StrUtils.format(B.DataModel.Instance.getParseFuncNameByType(e,this._codeLang),`lines[n + ${t+a}]`)}, ${x.link})`}else o+=`${I}.Get(${b.StrUtils.format(B.DataModel.Instance.getParseFuncNameByType(P.CSTypeEnum.Int,this._codeLang),`lines[n + ${t+a}]`)})`}else o+=b.StrUtils.format(B.DataModel.Instance.getParseFuncNameByType(e,this._codeLang),`lines[n + ${t+a}]`);t<r.length-1&&(o+=", ")}}if(2<=(null===o||void 0===o?void 0:o.length)&&", "==o.substring(o.length-2,o.length)&&(o=o.substring(0,o.length-2)),l.add(4,`var item = new ${d}(${o});`),f?(l.add(4,n+"[item.UniqueKey] = item;"),l.add(4,n+"_self[item.UniqueKey] = item;")):l.add(4,n+"[item.UniqueKey] = item;"),l.add(3,"}"),f?l.add(3,`${k} = new BaseConfig<${_}, ${d}>("${k}", ${n}_self);`,!1):l.add(3,`${k} = new BaseConfig<${_}, ${d}>("${k}", ${n});`,!1),!g.isSingleMainKey){var D=b.StrUtils.convertToUpperCamelCase(B.DataModel.MainKeyVarName),S=(s.newLine(),g.mainKeyNames),u=B.DataModel.Instance.getConfigCollectionTypeByIndex(k,this._codeLang),U=""+p+B.DataModel.Instance.config.export_collection_suffix;s.add(2,`public static ${u} ${U} { private set; get; }`),l.newLine(),l.add(3,U+` = new ${u}();`),l.add(3,`foreach (var keyValuePair in ${p}.Data)`),l.add(3,"{"),l.add(4,"var item = keyValuePair.Value;");for(let n=1;n<S.length;n++){l.add(4,"if (!"+U,!1);let t=n;for(let e=0;e<t;e++){var L=e+1;e!=t-1?l.add(0,`[item.${D}${L}]`,!1):l.add(0,`.ContainsKey(item.${D}${L})`,!1)}l.add(0,")"),l.add(5,U,!1),t=n;for(let e=0;e<t;e++){var T,K=e+1;e!=t-1?l.add(0,`[item.${D}${K}]`,!1):(T=B.DataModel.Instance.getConfigCollectionTypeByIndex(k,this._codeLang,n,S.length-1),l.add(0,`[item.${D}${K}] = new ${T}();`))}}l.add(4,U,!1);for(let e=0;e<S.length;e++){var w=e+1;e!=S.length-1?l.add(0,`[item.${D}${w}]`,!1):l.add(0,`[item.${D}${w}] = item;`)}l.add(3,"}")}}else{s.add(2,`public static ${k} ${k} { private set; get; }`,!1),r=Object.keys(i);let a="";r.forEach((e,t)=>{var n=i[e],n=B.DataModel.Instance.getValueType(n,this._codeLang,!1,k,e);a+=b.StrUtils.format(B.DataModel.Instance.getParseFuncNameByType(n,this._codeLang),`lines[${t}]`),t<r.length-1&&(a+=", ")}),l.add(3,`${k} = new ${k}("${k}", ${a});`,!1)}t<this._configNames.length-1&&(s.newLine(),l.newLine(2))}e=b.StrUtils.format(e,s.content,l.content);return O.IOUtils.writeTextFile(F.default.join(this._export.export_script_url,this._export.export_config_manager_name+"."+this._export.script_suffix),e,j.LineBreak.CRLF,`成功导出配置管理类脚本（${this._export.export_config_manager_name}）-> {0}`),!0}genConfigText(){let n=new A.CodeWriter;for(let e=0;e<this._configNames.length;e++){var a=this._configNames[e],t=B.DataModel.Instance.originConfig[a],i=t.fixed_keys;if(0<e&&n.add(0,"#",!1),i){var r=t.data;for(const l in r){var o=r[l];let e=isNaN(+l)?l:+l,t=(n.add(0,e),isNaN(+l)&&e.split("_"));var s=B.DataModel.Instance.remark[a];t&&1<t.length&&null!=s&&s.mainKeySubs&&t.length==s.mainKeySubs.length&&t.forEach(e=>{e=isNaN(+e)?e:+e,n.add(0,e)});for(let t=0;t<o.length;t++){let e=o[t];e=Array.isArray(e)?JSON.stringify(e):(null===e||void 0===e?void 0:e.toString())||"",n.add(0,""+e)}}}else for(const d in t){let e=t[d];e=Array.isArray(e)?JSON.stringify(e):null!=e&&(null===e||void 0===e?void 0:e.toString())||"",n.add(0,""+e)}}var e=c.default.parse(n.content),e=g.default.stringify(e);return O.IOUtils.writeTextFile(this._export.export_url,e,null,"导出配置文本成功！-> {0}"),!0}}exports.GenCSModule=t;