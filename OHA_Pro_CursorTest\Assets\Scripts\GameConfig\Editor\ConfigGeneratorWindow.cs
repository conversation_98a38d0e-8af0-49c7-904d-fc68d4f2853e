using UnityEngine;
using UnityEditor;
using System.Diagnostics;
using System.IO;

namespace GameConfig.Editor
{
    public class ConfigGeneratorWindow : EditorWindow
    {
        // 使用项目根目录的路径
        private string generatorPath = "GameConfig-main/Config/Generator";
        private string configPath = "Assets/StreamingAssets/GameConfig";
        private string outputPath = "Assets/Resources/Config";
        
        [MenuItem("Tools/GameConfig/配置生成器")]
        public static void ShowWindow()
        {
            var window = GetWindow<ConfigGeneratorWindow>("GameConfig 配置生成器");
            window.minSize = new Vector2(400, 400);
        }
        
        private void OnGUI()
        {
            EditorGUILayout.LabelField("GameConfig 配置生成器", EditorStyles.boldLabel);
            EditorGUILayout.Space();
            
            // 路径配置
            EditorGUILayout.LabelField("路径配置", EditorStyles.boldLabel);
            generatorPath = EditorGUILayout.TextField("生成器路径:", generatorPath);
            configPath = EditorGUILayout.TextField("配置表路径:", configPath);
            outputPath = EditorGUILayout.TextField("输出路径:", outputPath);
            
            EditorGUILayout.Space();
            
            // 状态显示
            EditorGUILayout.LabelField("状态检查", EditorStyles.boldLabel);
            
            string fullGeneratorPath = Path.GetFullPath(generatorPath);
            bool generatorExists = Directory.Exists(fullGeneratorPath);
            bool nodeJSAvailable = IsNodeJSAvailable();
            bool configDirExists = Directory.Exists(configPath);
            
            EditorGUILayout.LabelField($"生成器目录: {(generatorExists ? "✓" : "✗")}", 
                generatorExists ? EditorStyles.label : EditorStyles.boldLabel);
            EditorGUILayout.LabelField($"Node.js: {(nodeJSAvailable ? "✓" : "✗")}", 
                nodeJSAvailable ? EditorStyles.label : EditorStyles.boldLabel);
            EditorGUILayout.LabelField($"配置目录: {(configDirExists ? "✓" : "✗")}", 
                configDirExists ? EditorStyles.label : EditorStyles.boldLabel);
            
            EditorGUILayout.Space();
            
            // 操作按钮
            EditorGUILayout.LabelField("操作", EditorStyles.boldLabel);
            
            GUI.enabled = generatorExists && nodeJSAvailable;
            if (GUILayout.Button("生成配置", GUILayout.Height(30)))
            {
                GenerateConfigs();
            }
            GUI.enabled = true;
            
            EditorGUILayout.Space();
            
            if (GUILayout.Button("打开配置表目录"))
            {
                OpenConfigDirectory();
            }
            
            if (GUILayout.Button("打开生成器目录"))
            {
                OpenGeneratorDirectory();
            }
            
            if (GUILayout.Button("打开输出目录"))
            {
                OpenOutputDirectory();
            }
            
            if (GUILayout.Button("移动GameConfig到项目外部"))
            {
                MoveGameConfigOutside();
            }
            
            EditorGUILayout.Space();
            
            // 帮助信息
            EditorGUILayout.LabelField("使用说明", EditorStyles.boldLabel);
            EditorGUILayout.HelpBox(
                "1. 首次使用请点击'移动GameConfig到项目外部'按钮\n" +
                "2. 将Excel配置表放入配置表目录\n" +
                "3. 点击'生成配置'按钮生成配置文件\n" +
                "4. 在代码中使用ConfigMgr.Init(\"Config/Config\")初始化配置系统",
                MessageType.Info);
        }
        
        private void MoveGameConfigOutside()
        {
            try
            {
                string sourceDir = "Assets/Scripts/GameConfig-main";
                string targetDir = "../GameConfig-main";
                
                if (!Directory.Exists(sourceDir))
                {
                    EditorUtility.DisplayDialog("信息", "GameConfig-main目录不存在于Assets中", "确定");
                    return;
                }
                
                if (Directory.Exists(targetDir))
                {
                    if (!EditorUtility.DisplayDialog("确认", "目标目录已存在，是否覆盖？", "是", "否"))
                    {
                        return;
                    }
                    Directory.Delete(targetDir, true);
                }
                
                EditorUtility.DisplayProgressBar("移动文件", "正在移动GameConfig-main到项目外部...", 0.5f);
                
                // 使用系统命令移动目录
                ProcessStartInfo startInfo = new ProcessStartInfo()
                {
                    FileName = "cmd.exe",
                    Arguments = $"/c move \"{Path.GetFullPath(sourceDir)}\" \"{Path.GetFullPath(targetDir)}\"",
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    CreateNoWindow = true
                };
                
                using (Process process = Process.Start(startInfo))
                {
                    process.WaitForExit();
                    EditorUtility.ClearProgressBar();
                    
                    if (process.ExitCode == 0)
                    {
                        generatorPath = "../GameConfig-main/Config/Generator";
                        AssetDatabase.Refresh();
                        EditorUtility.DisplayDialog("成功", "GameConfig-main已移动到项目外部！", "确定");
                    }
                    else
                    {
                        string error = process.StandardError.ReadToEnd();
                        EditorUtility.DisplayDialog("错误", $"移动失败: {error}", "确定");
                    }
                }
            }
            catch (System.Exception ex)
            {
                EditorUtility.ClearProgressBar();
                EditorUtility.DisplayDialog("异常", $"移动过程中发生异常: {ex.Message}", "确定");
            }
        }
        
        private void GenerateConfigs()
        {
            try
            {
                string fullGeneratorPath = Path.GetFullPath(generatorPath);
                
                if (!Directory.Exists(fullGeneratorPath))
                {
                    EditorUtility.DisplayDialog("错误", $"生成器目录不存在: {fullGeneratorPath}", "确定");
                    return;
                }
                
                // 检查Node.js是否可用
                if (!IsNodeJSAvailable())
                {
                    EditorUtility.DisplayDialog("错误", "未找到Node.js，请确保已安装Node.js并添加到系统PATH中", "确定");
                    return;
                }
                
                EditorUtility.DisplayProgressBar("生成配置", "正在生成配置文件...", 0.3f);
                
                // 运行生成器
                ProcessStartInfo startInfo = new ProcessStartInfo()
                {
                    FileName = "node",
                    Arguments = "dist/main.js",
                    WorkingDirectory = fullGeneratorPath,
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    RedirectStandardError = true,
                    CreateNoWindow = true
                };
                
                using (Process process = Process.Start(startInfo))
                {
                    string output = process.StandardOutput.ReadToEnd();
                    string error = process.StandardError.ReadToEnd();
                    process.WaitForExit();
                    
                    EditorUtility.DisplayProgressBar("生成配置", "正在处理模板文件...", 0.7f);
                    
                    if (process.ExitCode == 0)
                    {
                        UnityEngine.Debug.Log($"[GameConfig] 配置生成成功!\n{output}");
                        
                        // 刷新资源数据库
                        AssetDatabase.Refresh();
                        
                        EditorUtility.ClearProgressBar();
                        EditorUtility.DisplayDialog("成功", "配置生成完成！", "确定");
                    }
                    else
                    {
                        EditorUtility.ClearProgressBar();
                        UnityEngine.Debug.LogError($"[GameConfig] 配置生成失败!\n错误信息: {error}\n输出信息: {output}");
                        EditorUtility.DisplayDialog("错误", $"配置生成失败!\n{error}", "确定");
                    }
                }
            }
            catch (System.Exception ex)
            {
                EditorUtility.ClearProgressBar();
                UnityEngine.Debug.LogError($"[GameConfig] 配置生成异常: {ex.Message}");
                EditorUtility.DisplayDialog("异常", $"配置生成异常: {ex.Message}", "确定");
            }
        }
        
        private bool IsNodeJSAvailable()
        {
            try
            {
                ProcessStartInfo startInfo = new ProcessStartInfo()
                {
                    FileName = "node",
                    Arguments = "--version",
                    UseShellExecute = false,
                    RedirectStandardOutput = true,
                    CreateNoWindow = true
                };
                
                using (Process process = Process.Start(startInfo))
                {
                    process.WaitForExit();
                    return process.ExitCode == 0;
                }
            }
            catch
            {
                return false;
            }
        }
        
        private void OpenConfigDirectory()
        {
            string fullPath = Path.GetFullPath(configPath);
            if (Directory.Exists(fullPath))
            {
                EditorUtility.RevealInFinder(fullPath);
            }
            else
            {
                EditorUtility.DisplayDialog("错误", $"目录不存在: {fullPath}", "确定");
            }
        }
        
        private void OpenGeneratorDirectory()
        {
            string fullPath = Path.GetFullPath(generatorPath);
            if (Directory.Exists(fullPath))
            {
                EditorUtility.RevealInFinder(fullPath);
            }
            else
            {
                EditorUtility.DisplayDialog("错误", $"目录不存在: {fullPath}", "确定");
            }
        }
        
        private void OpenOutputDirectory()
        {
            string fullPath = Path.GetFullPath(outputPath);
            if (Directory.Exists(fullPath))
            {
                EditorUtility.RevealInFinder(fullPath);
            }
            else
            {
                EditorUtility.DisplayDialog("错误", $"目录不存在: {fullPath}", "确定");
            }
        }
    }
} 