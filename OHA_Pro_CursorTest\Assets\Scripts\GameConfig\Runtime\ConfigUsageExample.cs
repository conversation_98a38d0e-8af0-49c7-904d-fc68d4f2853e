using UnityEngine;
using GameConfig;

/// <summary>
/// GameConfig使用示例
/// 展示如何初始化和使用配置系统
/// </summary>
public class ConfigUsageExample : MonoBehaviour
{
    void Start()
    {
        // 初始化配置系统
        InitializeConfig();
        
        // 使用配置数据
        UseConfigData();
    }
    
    /// <summary>
    /// 初始化配置系统
    /// </summary>
    private void InitializeConfig()
    {
        try
        {
            // 从Resources/Config/Config.txt加载配置
            ConfigMgr.Init("Config/Config");
            
            Debug.Log("[ConfigExample] ✅ 配置系统初始化成功！");
        }
        catch (System.Exception ex)
        {
            Debug.LogError($"[ConfigExample] ❌ 配置系统初始化失败: {ex.Message}");
        }
    }
    
    /// <summary>
    /// 使用配置数据示例
    /// </summary>
    private void UseConfigData()
    {
        if (ConfigMgr.KVConfig == null)
        {
            Debug.LogError("[ConfigExample] 配置未初始化！");
            return;
        }
        
        // 获取基本配置信息
        string gameName = ConfigMgr.KVConfig.GameName;
        string version = ConfigMgr.KVConfig.Version;
        
        Debug.Log($"[ConfigExample] 游戏名称: {gameName}");
        Debug.Log($"[ConfigExample] 版本号: {version}");
        
        // 获取数组配置
        var arrayA = ConfigMgr.KVConfig.A;
        Debug.Log($"[ConfigExample] 数组A长度: {arrayA.Count}");
        for (int i = 0; i < arrayA.Count; i++)
        {
            Debug.Log($"[ConfigExample] 数组A[{i}]: {arrayA[i]}");
        }
        
        // 获取二维数组配置
        var arrayB = ConfigMgr.KVConfig.B;
        Debug.Log($"[ConfigExample] 二维数组B长度: {arrayB.Count}");
        for (int i = 0; i < arrayB.Count; i++)
        {
            var subArray = arrayB[i];
            Debug.Log($"[ConfigExample] 二维数组B[{i}]长度: {subArray.Count}");
            for (int j = 0; j < subArray.Count; j++)
            {
                Debug.Log($"[ConfigExample] 二维数组B[{i}][{j}]: {subArray[j]}");
            }
        }
        
        // 获取字符串数组配置
        var arrayC = ConfigMgr.KVConfig.C;
        Debug.Log($"[ConfigExample] 字符串数组C长度: {arrayC.Count}");
        for (int i = 0; i < arrayC.Count; i++)
        {
            Debug.Log($"[ConfigExample] 字符串数组C[{i}]: {arrayC[i]}");
        }
        
        // 获取整数和布尔值配置
        int valueD = ConfigMgr.KVConfig.D;
        bool valueF = ConfigMgr.KVConfig.F;
        
        Debug.Log($"[ConfigExample] 整数D: {valueD}");
        Debug.Log($"[ConfigExample] 布尔值F: {valueF}");
    }
    
    /// <summary>
    /// 在Inspector中显示配置信息
    /// </summary>
    [ContextMenu("显示配置信息")]
    public void ShowConfigInfo()
    {
        if (ConfigMgr.KVConfig == null)
        {
            Debug.LogWarning("[ConfigExample] 请先运行游戏以初始化配置！");
            return;
        }
        
        Debug.Log("=== 配置信息 ===");
        Debug.Log($"配置名称: {ConfigMgr.KVConfig.ConfigName}");
        Debug.Log($"游戏名称: {ConfigMgr.KVConfig.GameName}");
        Debug.Log($"版本号: {ConfigMgr.KVConfig.Version}");
        Debug.Log($"数组A: [{string.Join(", ", ConfigMgr.KVConfig.A)}]");
        Debug.Log($"字符串数组C: [{string.Join(", ", ConfigMgr.KVConfig.C)}]");
        Debug.Log($"整数D: {ConfigMgr.KVConfig.D}");
        Debug.Log($"布尔值F: {ConfigMgr.KVConfig.F}");
        Debug.Log("===============");
    }
} 