# GameConfig故障排除指南

本文档提供GameConfig系统常见问题的解决方案。

## 目录
1. [Unity资源数据库错误](#unity资源数据库错误)
2. [编译错误](#编译错误)
3. [路径配置问题](#路径配置问题)
4. [模板文件问题](#模板文件问题)
5. [推荐工作流程](#推荐工作流程)

## Unity资源数据库错误

### 问题描述
Unity控制台出现大量错误：
- `Invalid format string parameter`
- `GetLoadedImportedAssetsAndArtifactIDs`
- `AssetDatabase.GetAssetDependencyHash`

### 原因
Unity为GameConfig-main目录中的所有文件（包括node_modules）创建了.meta文件，导致资源数据库混乱。

### 解决方案
1. **使用配置生成器工具**：
   - 打开 `Tools > GameConfig > 配置生成器`
   - 点击"移动GameConfig到项目外部"按钮

2. **手动移动**：
   ```bash
   # 将GameConfig-main移动到项目外部
   move "Assets/Scripts/GameConfig-main" "../GameConfig-main"
   ```

3. **清理Unity数据库**：
   - 删除Library文件夹
   - 重新打开Unity项目

## 编译错误

### 问题1：重复类定义
```
error CS0101: The namespace 'GameConfig' already contains a definition for 'BaseConfig'
```

**解决方案**：
1. 删除Example目录：`Assets/Scripts/GameConfig-main/Example`
2. 重命名模板文件为.template扩展名

### 问题2：类不存在
```
error CS0246: The type or namespace name 'ConfigUtility' could not be found
```

**解决方案**：
1. 使用配置生成器的"处理模板文件"功能
2. 手动将.template文件重命名为.cs文件

## 路径配置问题

### 问题描述
配置生成器找不到正确的路径。

### 解决方案
确保Config.json中的路径配置正确：
```json
{
  "export_script_url": "../../../Assets/Scripts/GameConfig/Runtime/"
}
```

## 模板文件问题

### 问题描述
GameConfig生成器总是生成`.cs.template`文件而不是`.cs`文件，导致编译错误。

### 原因分析
GameConfig使用模板系统来生成代码，模板文件存储在`templates/1/scripts/`目录中，文件名就是`.cs.template`格式。生成器会复制模板文件到目标目录，保持原始的`.template`扩展名。

### 解决方案

#### 方法1：修复模板文件名（推荐）
使用配置生成器的"修复模板文件名问题"按钮：
1. 打开 `Tools > GameConfig > 配置生成器`
2. 点击"修复模板文件名问题"按钮
3. 这会将生成器模板目录中的`.cs.template`文件重命名为`.cs`文件
4. 之后生成器将直接生成`.cs`文件

#### 方法2：自动处理模板文件
如果已经生成了`.template`文件：
1. 使用配置生成器的"处理模板文件"按钮
2. 这会将Runtime目录中的`.cs.template`文件重命名为`.cs`文件

#### 方法3：手动修复
```bash
# 在生成器模板目录中
cd GameConfig-main/Config/Generator/templates/1/scripts
rename "*.cs.template" "*.cs"
```

### 模板文件处理流程
1. **首次设置**：点击"修复模板文件名问题"
2. **生成配置**：点击"生成配置"（会自动处理模板文件）
3. **如有问题**：点击"处理模板文件"或"清理模板文件"

## 推荐工作流程

### 首次安装
1. 将GameConfig-main放置在项目根目录外
2. 使用配置生成器的"修复模板文件名问题"功能
3. 配置Excel文件路径
4. 生成配置文件

### 日常使用
1. 修改Excel配置表
2. 点击"生成配置"按钮
3. 系统自动处理所有模板文件
4. 无需手动干预

### 故障恢复
1. 如果出现编译错误，使用"处理模板文件"
2. 如果Unity出现资源错误，使用"移动GameConfig到项目外部"
3. 如果生成器总是生成.template文件，使用"修复模板文件名问题"

## 工具说明

配置生成器提供以下工具：
- **生成配置**：运行配置生成器并自动处理模板文件
- **处理模板文件**：将Runtime目录中的.template文件重命名为.cs文件
- **清理模板文件**：删除所有.template文件
- **修复模板文件名问题**：修复生成器模板，让其直接生成.cs文件
- **移动GameConfig到项目外部**：解决Unity资源数据库问题

## 常见问题FAQ

**Q: 为什么会生成.template文件？**
A: 这是GameConfig生成器的默认行为，模板文件名就是.template格式。使用"修复模板文件名问题"可以解决。

**Q: 可以完全避免.template文件吗？**
A: 可以，使用"修复模板文件名问题"功能后，生成器将直接生成.cs文件。

**Q: 每次都需要手动处理模板文件吗？**
A: 不需要，配置生成器会自动处理。只有在出现问题时才需要手动干预。 